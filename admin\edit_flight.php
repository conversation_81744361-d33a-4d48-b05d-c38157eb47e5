<?php
session_start();
require("../database.php");

// Fetch the event_mgm_id from the URL
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
if (!$event_mgm_id) {
    die('Event Management ID not provided.');
}
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as total_records, 
                     SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
              FROM event_role_mgm 
              WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($total_records, $match_event);
    $stmt->fetch();
    $stmt->close();

    if ($total_records > 0) {
        // Profile ID is registered in event_role_mgm
        if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
            // If event_mgm_id doesn't match or role is not gPHOfKV0sL
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } else {
        // Profile ID not registered in event_role_mgm
        if ($admin_roleid != 'gPHOfKV0sL') {
            // If role is not gPHOfKV0sL, deny access
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch all event_id associated with the given event_mgm_id
$event_query = "
    SELECT em.event_id, em.event_num_participant
    FROM event_mgm AS em
    WHERE em.event_mgm_id = ?
";
$stmt_event = $conn->prepare($event_query);
$stmt_event->bind_param("s", $event_mgm_id);
$stmt_event->execute();
$event_result = $stmt_event->get_result();

if ($event_result->num_rows == 0) {
    die('Invalid event_mgm_id or no event found.');
}

$event_ids = [];
$event_num_participant = null;
while ($event = $event_result->fetch_assoc()) {
    $event_ids[] = $event['event_id'];
    // Assuming event_num_participant is the same for all event_id under the same event_mgm_id
    if (is_null($event_num_participant)) {
        $event_num_participant = $event['event_num_participant'];
    }
}

// Prepare placeholders and types for IN clause
$in_placeholders = implode(',', array_fill(0, count($event_ids), '?'));
$types = str_repeat('s', count($event_ids));

// Fetch course_id(s) from custom_hole table
$course_query = "
    SELECT DISTINCT course_id
    FROM custom_hole
    WHERE event_id IN ($in_placeholders)
";
$stmt_course = $conn->prepare($course_query);
$stmt_course->bind_param($types, ...$event_ids);
$stmt_course->execute();
$course_result = $stmt_course->get_result();

$course_ids = [];
if ($course_result->num_rows > 0) {
    while ($course = $course_result->fetch_assoc()) {
        $course_ids[] = $course['course_id'];
    }
}

// Fetch all course_ids and their names into an associative array
$course_id_name_map = [];
if (count($course_ids) > 0) {
    $course_in_placeholders = implode(',', array_fill(0, count($course_ids), '?'));
    $course_types = str_repeat('s', count($course_ids));

    $course_name_query = "
        SELECT course_id, course_name
        FROM course_info
        WHERE course_id IN ($course_in_placeholders)
    ";
    $stmt_course_name = $conn->prepare($course_name_query);
    $stmt_course_name->bind_param($course_types, ...$course_ids);
    $stmt_course_name->execute();
    $course_name_result = $stmt_course_name->get_result();

    while ($course_info = $course_name_result->fetch_assoc()) {
        $course_id_name_map[$course_info['course_id']] = $course_info['course_name'];
    }
}

// Fetch distinct flight_list_names for the dropdown
$flight_list_name_query = "
    SELECT DISTINCT flight_list_name
    FROM flight_list
    WHERE event_id IN ($in_placeholders)
    ORDER BY flight_list_name ASC
";
$stmt_flight_list_name = $conn->prepare($flight_list_name_query);
$stmt_flight_list_name->bind_param($types, ...$event_ids);
$stmt_flight_list_name->execute();
$flight_list_name_result = $stmt_flight_list_name->get_result();

$flight_list_names = [];
if ($flight_list_name_result) {
    while ($row = $flight_list_name_result->fetch_assoc()) {
        $flight_list_names[] = $row['flight_list_name'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Edit Draw List</title>
    <!-- Include Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Include jQuery (Full Version) -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- Include Bootstrap JS Bundle (Includes Popper.js) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Include Font Awesome for icons -->
    <link rel="stylesheet"
     href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Include Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Include Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Font Size Adjustment */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Main Title Styling */
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 24px 0;
            text-align: center;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        /* 3. Professional Card Layout */
        .control-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        /* 4. Header Controls Styling */
        .header-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }

        .filter-section {
            display: flex;
            gap: 16px;
            align-items: center;
            justify-content: center;
            width: 100%;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-control {
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
            min-width: 200px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        /* 5. Professional Button Styling */
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }
        /* 6. Flight Detail Box Styling */
        .flight-detail {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 20px;
            padding: 20px;
            font-size: 0.875rem;
        }

        .flight-detail h5 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }

        .flight-detail h6 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 16px;
            text-align: center;
        }

        /* 7. No Records Message Styling */
        .no-records-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            background-color: #f9fafb;
            border-radius: 12px;
            border: 2px dashed #d1d5db;
            font-size: 0.875rem;
        }

        .no-records-message {
            color: #6b7280;
            font-weight: 500;
        }

        /* 8. Professional Table Styling */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            margin-bottom: 16px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: white;
        }

        table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            vertical-align: middle;
        }

        table tbody tr:hover {
            background-color: #f9fafb;
        }

        table tbody tr:last-child td {
            border-bottom: none;
        }
        /* 9. Professional Section Headers */
        .section-header {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin: 32px 0 24px 0;
            text-align: center;
        }

        .start-header {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .flight-date {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .course-name {
            font-size: 1rem;
            color: #4b5563;
            font-weight: 500;
        }

        /* 10. Add Player Section Styling */
        .add-player-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .add-player-section label {
            margin-bottom: 0;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .add-player-section .add-player-select {
            flex: 1;
            min-width: 200px;
        }

        .add-player-section .add-player-btn {
            flex-shrink: 0;
        }

        /* 11. Interactive Elements */
        .captain-checkbox {
            cursor: pointer;
            width: 16px;
            height: 16px;
        }

        .edit-flight-icon {
            background: none;
            border: none;
            color: #3b82f6;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .edit-flight-icon:hover {
            background: #eff6ff;
            color: #2563eb;
        }

        .edit-flight-icon i {
            font-size: 1rem;
        }

        /* 12. Loading states */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 13. Modal Styling */
        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            border-radius: 12px 12px 0 0;
            padding: 20px 24px;
        }

        .modal-title {
            font-weight: 600;
            color: #1f2937;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            border-radius: 0 0 12px 12px;
            padding: 16px 24px;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        /* 14. Responsive Design */
        @media (max-width: 768px) {
            .header-controls {
                flex-direction: column;
                gap: 16px;
            }

            .filter-section {
                flex-direction: column;
                gap: 12px;
                justify-content: center;
            }

            .form-control {
                min-width: 100%;
            }

            .flight-detail {
                padding: 16px;
            }

            .section-header {
                padding: 16px;
            }

            .main-title {
                font-size: 1.5rem;
            }

            .add-player-section {
                flex-direction: column;
                align-items: stretch;
            }

            .add-player-section .add-player-select {
                min-width: 100%;
            }
        }

        @media (max-width: 600px) {
            .control-card {
                padding: 16px;
            }

            .filter-section {
                gap: 8px;
            }

            .form-group label {
                font-size: 0.7rem;
            }

            table th, table td {
                font-size: 0.75rem !important;
                padding: 8px 6px !important;
            }

            table th {
                padding: 10px 6px !important;
            }

            .flight-detail {
                padding: 12px !important;
            }

            .flight-detail h5 {
                font-size: 1rem;
            }

            .flight-detail h6 {
                font-size: 0.8rem;
            }

            .section-header {
                padding: 12px;
            }

            .start-header {
                font-size: 1.25rem;
            }
        }

        /* Print Styles */
        @media print {
            a {
                text-decoration: none; /* Remove underlines from links */
                color: black; /* Change link color to black */
            }
            a[href]:after {
                content: none !important; /* Hide the URL after links */
            }
            a[href]:before {
                content: none !important; /* Hide any additional URL content */
            }
            /* Hide browser headers/footers if allowed by the browser */
            @page {
                margin: 0; /* Attempt to remove page headers/footers if supported */
            }
            /* Increase the font size of the start header */
            .start-header {
                font-size: 2rem !important; /* Adjust the size as needed */
            }
            /* Increase the font size of the course name */
            .course-name {
                font-size: 1.5rem !important; /* Adjust the size as needed */
            }
        }


    </style>
</head>
<body class="bg-gray-50">
    <!-- Example: include header -->
    <?php include("admin_header.php"); ?>

    <div class="header-section">
        <h2 class="main-title">Edit Draw List</h2>
    </div>

    <div class="container">
        <div class="control-card">
            <div class="header-controls">
                <div class="filter-section">
                    <!-- Flight List Name Dropdown -->
                    <div class="form-group">
                        <label for="flightListName">Flight List</label>
                        <select class="form-control" id="flightListName" name="flight_list_name">
                            <option value="" disabled selected>Select Flight List Name</option>
                            <?php foreach ($flight_list_names as $name): ?>
                                <option value="<?php echo htmlspecialchars($name); ?>">
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Round Filter Dropdown -->
                    <div class="form-group">
                        <label for="roundFilter">Round</label>
                        <select class="form-control" id="roundFilter" disabled>
                            <option value="" disabled selected>Select Round</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div id="flightsContainer">
            <div class="no-records-wrapper">
                <div class="no-records-message">No records found.</div>
            </div>
        </div>
    </div>

    <!-- Example: include footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Add Flight Modal -->
    <div class="modal fade" id="addFlightModal" tabindex="-1" aria-labelledby="addFlightModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="addFlightForm">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addFlightModalLabel">Add New Flight</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- Flight Name -->
                        <div class="mb-3">
                            <label for="flightName" class="form-label">Flight Name</label>
                            <input type="text" class="form-control" id="flightName" name="flight_name" required>
                        </div>
                        <!-- Flight Time -->
                        <div class="mb-3">
                            <label for="flightTime" class="form-label">Flight Time</label>
                            <input type="time" class="form-control" id="flightTime" name="flight_time" required>
                        </div>
                        <!-- Player Selection with Max 4 and Search -->
                        <div class="mb-3">
                            <label for="selectPlayers" class="form-label">Select Players (Max: 4)</label>
                            <select class="form-select select2" id="selectPlayers" name="players[]" multiple="multiple" required>
                                <!-- Players will be populated dynamically -->
                            </select>
                            <small class="form-text text-muted">You can select up to 4 players for this flight.</small>
                        </div>
                        <!-- Display All Selected Players -->
                        <div class="mb-3">
                            <label class="form-label">Selected Players:</label>
                            <ul id="selectedPlayersList" class="list-disc pl-5">
                                <li>None selected</li>
                            </ul>
                        </div>
                        <!-- Hidden Fields -->
                        <div class="mb-3">
                            <input type="hidden" id="flightDate" name="flight_date">
                            <input type="hidden" id="teeBox" name="tee_box">
                            <input type="hidden" id="startPoint" name="start_point">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Add Flight</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Flight Modal -->
    <div class="modal fade" id="editFlightModal" tabindex="-1" aria-labelledby="editFlightModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-md">
        <div class="modal-content">
          <form id="editFlightForm">
            <div class="modal-header">
              <h5 class="modal-title" id="editFlightModalLabel">Edit Flight</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
              </button>
            </div>

            <div class="modal-body">
              <!-- Flight ID (hidden) -->
              <input type="hidden" id="editFlightId" name="flight_id">

              <!-- Round Number (hidden) -->
              <input type="hidden" id="editRoundNumber" name="round_number">

              <!-- Flight List Name (hidden or show if you want it editable) -->
              <input type="hidden" id="editFlightListName" name="flight_list_name">

              <!-- Flight Name -->
              <div class="mb-3">
                <label for="editFlightName" class="form-label">Flight Name</label>
                <input type="text" class="form-control" id="editFlightName" name="flight_name" required>
              </div>

              <!-- Flight Time -->
              <div class="mb-3">
                <label for="editFlightTime" class="form-label">Flight Time</label>
                <input type="time" class="form-control" id="editFlightTime" name="flight_time" required>
              </div>

              <!-- Tee Box -->
              <div class="mb-3">
                <label for="editTeeBox" class="form-label">Tee Box</label>
                <input type="text" class="form-control" id="editTeeBox" name="tee_box" required>
              </div>

              <!-- (Optional) Flight Date, if you want to allow editing -->
              <input type="hidden" id="editFlightDate" name="flight_date">
            </div>

            <div class="modal-footer">
              <button type="submit" class="btn btn-primary">Update Flight</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- JavaScript Variables -->
    <script>
    // Pass PHP variables to JavaScript
    var courseIdNameMap = <?php echo json_encode($course_id_name_map); ?>;
    var eventMgmId = '<?php echo $event_mgm_id; ?>';
    var eventIds = <?php echo json_encode($event_ids); ?>;
    </script>

    <!-- Custom JavaScript -->
    <script>
    $(document).ready(function () {
        let selectedRound = null; // To track the selected round

        // Initialize Select2 on the players select element
        $('#selectPlayers').select2({
            placeholder: "Select Players",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#addFlightModal'),
        });

        // Handle reordering of selected players visually in the Add Flight modal
        $('#selectPlayers').on('select2:select', function (e) {
            const selectedOption = e.params.data.element;
            $(selectedOption).appendTo($(this));
            $(this).trigger('change.select2');
            updateSelectedPlayersList();
        });
        $('#selectPlayers').on('select2:unselect', function () {
            updateSelectedPlayersList();
        });
        function updateSelectedPlayersList() {
            const selectedOptions = $('#selectPlayers').find('option:selected');
            const selectedList = $('#selectedPlayersList');
            selectedList.empty();

            if (selectedOptions.length > 0) {
                selectedOptions.each(function () {
                    selectedList.append(`<li>${$(this).text()}</li>`);
                });
            } else {
                selectedList.append('<li>None selected</li>');
            }
        }

        // Populate Rounds when flight list name changes
        $('#flightListName').on('change', function () {
            const selectedFlightListName = $(this).val();
            if (selectedFlightListName) {
                $('#roundFilter').prop('disabled', true).html('<option value="" disabled selected>Select Round</option>');

                $.ajax({
                    url: 'fetch_rounds.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        'event_ids[]': eventIds,
                        flight_list_name: selectedFlightListName
                    },
                    traditional: true,
                    success: function (response) {
                        let rounds;
                        try {
                            rounds = JSON.parse(response);
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            alert('Failed to fetch rounds.');
                            return;
                        }

                        if (rounds.length > 0) {
                            let options = '<option value="" disabled selected>Select Round</option>';
                            rounds.forEach(function (round) {
                                options += `<option value="${round.round_number}">Round ${round.round_number}</option>`;
                            });
                            $('#roundFilter').html(options).prop('disabled', false);
                        } else {
                            alert('No rounds found for the selected flight list name.');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Failed to fetch rounds:', error);
                        alert('Failed to fetch rounds. Please try again.');
                    }
                });
            }
        });

        // Fetch and display flights when round changes
        $('#roundFilter').on('change', function () {
            const selectedRoundNumber = $(this).val();
            const selectedFlightListName = $('#flightListName').val();
            selectedRound = selectedRoundNumber; // store globally

            if (selectedRoundNumber && selectedFlightListName) {
                showLoader();

                $.ajax({
                    url: 'fetch_flights.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        event_ids: eventIds,
                        round_number: selectedRoundNumber,
                        flight_list_name: selectedFlightListName
                    },
                    success: function (response) {
                        hideLoader();
                        let flightData;
                        try {
                            flightData = JSON.parse(response);
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            $('#flightsContainer').html('<div class="alert alert-danger text-center">Invalid response from server.</div>');
                            return;
                        }

                        if (flightData.error) {
                            alert(flightData.error);
                        } else if (!flightData.flights || flightData.flights.length === 0) {
                            $('#flightsContainer').html('<div class="no-records-wrapper"><div class="no-records-message">No flights found.</div></div>');
                        } else {
                            renderFlights(flightData.flights, selectedRoundNumber, selectedFlightListName, flightData.display_score);
                        }
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        console.error('Failed to fetch flights:', error);
                        $('#flightsContainer').html('<div class="alert alert-danger text-center">Failed to fetch flight data. Please try again.</div>');
                    }
                });
            }
        });

        function showLoader() {
            $('#flightsContainer').html(`
                <div class="control-card text-center">
                    <div class="loading-spinner mx-auto mb-3"></div>
                    <div style="color: #6b7280; font-weight: 500;">Loading flight data...</div>
                </div>
            `);
        }
        function hideLoader() {
            $('.loader, .loading-spinner').parent().remove();
        }

        // Main function to render flights
        function renderFlights(flightData, roundNumber, flightListName, displayScore) {
            const flightsContainer = $('#flightsContainer');
            flightsContainer.empty();

            if (flightData.length === 0) {
                flightsContainer.append('<div class="no-records-wrapper"><div class="no-records-message">No records found.</div></div>');
                return;
            }

            // get date from first flight
            const flightDate = flightData[0].flight_date;
            const formattedDate = formatDate(flightDate);

            // group by start point
            const groupedByStartPoint = {};
            flightData.forEach(flight => {
                if (!groupedByStartPoint[flight.start_point]) {
                    groupedByStartPoint[flight.start_point] = [];
                }
                groupedByStartPoint[flight.start_point].push(flight);
            });

            // loop each start point
            for (const startPoint in groupedByStartPoint) {
                const flights = groupedByStartPoint[startPoint];

                // gather unique course names
                const courseNames = new Set();
                flights.forEach(f => {
                    const cid = String(f.course_id);
                    const cname = courseIdNameMap[cid] || 'Unknown Course';
                    courseNames.add(cname);
                });
                const displayedCourseNames = Array.from(courseNames).join(', ');

                // Headers with new professional styling
                flightsContainer.append(`
                    <div class="section-header">
                        <h3 class="start-header">Round ${roundNumber} - ${ucfirst(startPoint)} Start</h3>
                        <div class="flight-date">Date: ${formattedDate}</div>
                        <div class="course-name">Course: ${displayedCourseNames}</div>
                    </div>
                `);

                // group flights by flight_name
                const groupedFlights = {};
                flights.forEach(flight => {
                    if (!groupedFlights[flight.flight_name]) {
                        groupedFlights[flight.flight_name] = [];
                    }
                    groupedFlights[flight.flight_name].push(flight);
                });

                // Depending on your start_point logic, call one of the following:
                if (startPoint.toLowerCase().includes('shotgun')) {
                    renderShotgunGroupBoxes(groupedFlights, roundNumber, displayScore);
                } else if (startPoint.toLowerCase().includes('one point')) {
                    renderOnePointGroupBoxes(groupedFlights, roundNumber, displayScore);
                } else if (startPoint.toLowerCase().includes('two point')) {
                    renderTwoPointGroupBoxes(groupedFlights, roundNumber, displayScore);
                } else if (startPoint.toLowerCase().includes('customize')) {
                    renderCustomizeGroupBoxes(groupedFlights, roundNumber, displayScore);
                } else {
                    console.error('Unknown start point:', startPoint);
                }
            }
        }

        function formatDate(dateString) {
            const dateObj = new Date(dateString);
            if (isNaN(dateObj.getTime())) {
                return 'Invalid Date';
            }
            const day = String(dateObj.getDate()).padStart(2, '0');
            const year = dateObj.getFullYear();
            const monthNames = [
                'January','February','March','April','May','June',
                'July','August','September','October','November','December'
            ];
            const monthName = monthNames[dateObj.getMonth()];
            return `${day} ${monthName} ${year}`;
        }
        function formatTimeToAMPM(time) {
            const date = new Date('1970-01-01T' + time);
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            return hours + ':' + minutes + ' ' + ampm;
        }
        function ucfirst(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // SHOTGUN
        function renderShotgunGroupBoxes(groupedFlights, roundNumber, displayScore) {
    // Create two columns for the layout
    const col1 = $('<div class="col-lg-6 mb-3"></div>');
    const col2 = $('<div class="col-lg-6 mb-3"></div>');
    let playerNumber = 1;

    // Group flights by tee box using the tee_box value from flightInfo.
    // The tee_box value might be something like "TeeBox 1", "TeeBox 2", "10", or "TeeBox 10A"
    const groupedByTeeBox = {};
    for (const flightName in groupedFlights) {
        const flightDetails = groupedFlights[flightName];
        const flightInfo = flightDetails[0];
        const teeBox = flightInfo.tee_box; // Use the provided tee_box value
        if (!groupedByTeeBox[teeBox]) {
            groupedByTeeBox[teeBox] = [];
        }
        groupedByTeeBox[teeBox].push({
            flightDetails,
            flightName: flightInfo.flight_name,
            flightTime: flightInfo.flight_time,
            flightId: flightInfo.flight_id,
            startPoint: flightInfo.start_point,
            flightListName: flightInfo.flight_list_name,
            flightDate: flightInfo.flight_date
        });
    }

    // Helper to robustly extract a numeric value from a tee box string.
    // For example, both "TeeBox 10A" and "10" will yield 10.
    const getTeeBoxNumber = (str) => {
        str = str.trim();
        if (/^\d+$/.test(str)) {
            return parseInt(str, 10);
        }
        const match = str.match(/\d+/);
        return match ? parseInt(match[0], 10) : 0;
    };

    // Sort the tee boxes based on their numeric value
    const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
        return getTeeBoxNumber(a) - getTeeBoxNumber(b);
    });

    // For column assignment, we'll divide the sorted tee boxes into two groups
    // (for example, if there are only 2 tee boxes, the first goes to col1 and the second to col2).
    const middleIndex = Math.ceil(sortedTeeBoxes.length / 2);

    // Process each tee box group using its sorted order index
    sortedTeeBoxes.forEach((teeBox, index) => {
        const teeFlights = groupedByTeeBox[teeBox];
        const flightDetailDiv = $('<div class="flight-detail"></div>');
        flightDetailDiv.append('<h5>' + teeBox + '</h5>');

        // Add a Flight button for this tee box
        const addFlightButton = $(`
            <button class="btn btn-secondary add-flight-btn mb-3" 
                data-tee-box="${teeBox}" 
                data-round-number="${roundNumber}">
                Add Flight
            </button>
        `);
        flightDetailDiv.append(addFlightButton);

        // Sort flights within this tee box numerically based on the number found in the flight name.
        const sortedFlights = teeFlights.sort((a, b) => {
            const matchA = a.flightName.match(/\d+/);
            const matchB = b.flightName.match(/\d+/);
            const numA = matchA ? parseInt(matchA[0], 10) : 0;
            const numB = matchB ? parseInt(matchB[0], 10) : 0;
            return numA - numB;
        });

        // Render each flight within this tee box group
        sortedFlights.forEach((flight) => {
            const { flightId, flightName, flightTime, startPoint } = flight;

            flightDetailDiv.append(`
                <h6>
                    <strong>${flightName} (Time: ${formatTimeToAMPM(flightTime)})</strong>
                    &nbsp;
                    <button class="edit-flight-icon edit-flight-btn"
                        data-flight-id="${flightId}"
                        data-flight-name="${flightName}"
                        data-flight-time="${flightTime}"
                        data-tee-box="${teeBox}"
                        data-flight-list-name="${flight.flightListName}"
                        data-flight-date="${flight.flightDate}"
                        data-round-number="${roundNumber}">
                        <i class="fas fa-edit"></i>
                    </button>
                </h6>
            `);

            let addPlayerSection = '';
            if (flight.flightDetails.length < 4) {
                addPlayerSection = `
                    <div class="add-player-section mb-3">
                        <label class="add-player-label">Add Player:</label>
                        <select class="form-control add-player-select"
                            data-flight-id="${flightId}"
                            data-round-number="${roundNumber}"
                            data-flight-list-name="${flight.flightListName}"
                            data-flight-date="${flight.flightDate}">
                            <option value="" disabled selected>Select Player</option>
                        </select>
                        <button class="btn btn-primary add-player-btn"
                            data-flight-id="${flightId}"
                            data-round-number="${roundNumber}"
                            data-flight-list-name="${flight.flightListName}"
                            data-flight-date="${flight.flightDate}">
                            Add Player
                        </button>
                    </div>
                `;
            }

            const table = $(`
                <div class="table-responsive">
                    ${addPlayerSection}
                    <table class="table">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Player Name</th>
                                <th>${displayScore === 1 ? 'Score' : 'Handicap'}</th>
                                <th>Category</th>
                                <th>Captain</th>
                                <th>
                                    <input type="checkbox" class="select-all-checkbox" title="Select All">
                                    <br><small>Select All</small>
                                </th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            `);

            const tbody = table.find('tbody');

            flight.flightDetails.forEach(player => {
                const isCaptain = parseInt(player.captain_id) === 1 ? 'checked' : '';
                // Create a unique flight identifier from various parameters
                const flightIdentifier = `${flightName}_${flightTime}_${teeBox}_${roundNumber}_${startPoint}`;
                tbody.append(`
                    <tr>
                        <td>${playerNumber}</td>
                        <td>${player.player_name || 'No player assigned'}</td>
                        <td>${displayScore === 1 ? player.total_strokes : (player.player_handicap || 'N/A')}</td>
                        <td>${player.category || 'N/A'}</td>
                        <td>
                            <input type="checkbox" class="captain-checkbox"
                                data-flight-id="${player.flight_id}"
                                data-flight-identifier="${flightIdentifier}" ${isCaptain}>
                        </td>
                        <td>
                            <input type="checkbox" class="delete-checkbox"
                                data-flight-id="${player.flight_id}"
                                data-form-id="${player.form_id}"
                                data-round-number="${roundNumber}">
                        </td>
                    </tr>
                `);
                playerNumber++;
            });
            flightDetailDiv.append(table);

            // Add a button to delete selected players
            flightDetailDiv.append(`
                <button class="btn btn-danger delete-selected-players mt-2" 
                    data-round-number="${roundNumber}">
                    Delete Players
                </button>
            `);

            // Optionally, if the flight has fewer than 4 players, load available players.
            if (flight.flightDetails.length < 4) {
                loadAvailablePlayers(flightId, roundNumber);
            }

            // Assign the tee box group to a column based on its sorted order.
            // For example, if there are only 2 tee boxes, index 0 goes to col1 and index 1 goes to col2.
            if (index < middleIndex) {
                col1.append(flightDetailDiv);
            } else {
                col2.append(flightDetailDiv);
            }
        });

    });

    // Append both columns to the flights container
    const row = $('<div class="row"></div>');
    row.append(col1).append(col2);
    $('#flightsContainer').append(row);
}


 // Function to render One Point group boxes
 function renderOnePointGroupBoxes(groupedFlights, roundNumber, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');
            let playerNumber = 1; // Initialize player number globally

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightName in groupedFlights) {
                const flightDetails = groupedFlights[flightName];
                const flightInfo = flightDetails[0];

                const teeBox = flightInfo.tee_box;
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }

                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time,
                    flightId: flightInfo.flight_id,
                    startPoint: flightInfo.start_point,
                    flightListName: flightInfo.flight_list_name,
                    flightDate: flightInfo.flight_date
                });
            }

            // Sort TeeBoxes in numerical order
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const teeBoxA = parseInt(a.replace('TeeBox ', ''));
                const teeBoxB = parseInt(b.replace('TeeBox ', ''));
                return teeBoxA - teeBoxB;
            });

            // Iterate through each sorted TeeBox and render flights
            sortedTeeBoxes.forEach((teeBox, index) => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');

                // Display the TeeBox heading
                flightDetailDiv.append('<h5>' + teeBox + '</h5>');

                // Add Flight button under TeeBox heading
                const addFlightButton = $(`
                    <button class="btn btn-secondary add-flight-btn mb-3" data-tee-box="${teeBox}" data-round-number="${roundNumber}">
                        Add Flight
                    </button>
                `);
                flightDetailDiv.append(addFlightButton);

                // Sort flightIds in numerical order based on flight name (e.g., A1, B1)
                const sortedFlights = teeFlights.sort((a, b) => {
                    const numberA = parseInt(a.flightName.replace(/[^\d]/g, ''), 10);
                    const numberB = parseInt(b.flightName.replace(/[^\d]/g, ''), 10);
                    return numberA - numberB;
                });

                // Iterate through each sorted flight under this TeeBox
                sortedFlights.forEach((flight) => {
                    const flightId = flight.flightId;
                    const flightName = flight.flightName;
                    const flightTime = flight.flightTime;
                    const startPoint = flight.startPoint;

                    // Flight heading
                    flightDetailDiv.append(`
                        <h6>
                          <strong>${flightName} (Time: ${formatTimeToAMPM(flightTime)})</strong>
                          &nbsp;
                          <!-- EDIT BUTTON -->
                          <button class="edit-flight-icon edit-flight-btn"
                                  data-flight-id="${flightId}"
                                  data-flight-name="${flightName}"
                                  data-flight-time="${flightTime}"
                                  data-tee-box="${teeBox}"
                                  data-flight-list-name="${flight.flightListName}"
                                  data-flight-date="${flight.flightDate}"
                                  data-round-number="${roundNumber}">
                            <i class="fas fa-edit"></i>
                          </button>
                        </h6>
                    `);

                    let addPlayerSection = '';
                    if (flight.flightDetails.length < 4) {
                        addPlayerSection = `
                            <div class="add-player-section mb-3">
                                <label class="add-player-label">Add Player:</label>
                                <select class="form-control add-player-select"
                                    data-flight-id="${flightId}"
                                    data-round-number="${roundNumber}"
                                    data-flight-list-name="${flight.flightListName}"
                                    data-flight-date="${flight.flightDate}">
                                    <option value="" disabled selected>Select Player</option>
                                </select>
                                <button class="btn btn-primary add-player-btn"
                                    data-flight-id="${flightId}"
                                    data-round-number="${roundNumber}"
                                    data-flight-list-name="${flight.flightListName}"
                                    data-flight-date="${flight.flightDate}">
                                    Add Player
                                </button>
                            </div>
                        `;
                    }

                    const table = $(`
                        <div class="table-responsive">
                            ${addPlayerSection}
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'Handicap'}</th>
                                        <th>Category</th>
                                        <th>Captain</th>
                                        <th>
                                        <input type="checkbox" class="select-all-checkbox" title="Select All">
                                        <br><small>Select All</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');

                    // List each player within this flight
                    flight.flightDetails.forEach((player) => {
                        const isCaptain = parseInt(player.captain_id) === 1 ? 'checked' : '';

                        // Create a unique flight identifier
                        const flightIdentifier = `${flightName}_${flightTime}_${teeBox}_${roundNumber}_${startPoint}`;

                        tbody.append(`
                            <tr>
                                <td>${playerNumber}</td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                <td>${displayScore === 1 ? player.total_strokes : (player.player_handicap || 'N/A')}</td>
                                <td>${player.category || 'N/A'}</td>
                                <td>
                                    <input type="checkbox" class="captain-checkbox"
                                        data-flight-id="${player.flight_id}"
                                        data-flight-identifier="${flightIdentifier}" ${isCaptain}>
                                </td>
                                <td>
                                    <input type="checkbox" class="delete-checkbox"
                                        data-flight-id="${player.flight_id}"
                                        data-form-id="${player.form_id}"
                                        data-round-number="${roundNumber}">
                                </td>
                            </tr>
                        `);
                        playerNumber++; // Increment player number globally
                    });

                    flightDetailDiv.append(table);

                    // Add 'Delete Selected Players' button after the table
                    flightDetailDiv.append('<button class="btn btn-danger delete-selected-players mt-2" data-round-number="' + roundNumber + '">Delete Players</button>');

                    if (flight.flightDetails.length < 4) {
                        loadAvailablePlayers(flightId, roundNumber);
                    }
                });

                // Assign TeeBoxes to alternating columns
                const middleIndex = Math.ceil(sortedTeeBoxes.length / 2);
                if (index < middleIndex) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Function to render Two Point group boxes
        function renderTwoPointGroupBoxes(groupedFlights, roundNumber, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');
            let playerNumber = 1; // Initialize player number globally

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightName in groupedFlights) {
                const flightDetails = groupedFlights[flightName];
                const flightInfo = flightDetails[0]; // Get the first flight

                const teeBox = flightInfo.tee_box;
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }

                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time,
                    flightId: flightInfo.flight_id,
                    startPoint: flightInfo.start_point,
                    flightListName: flightInfo.flight_list_name,
                    flightDate: flightInfo.flight_date
                });
            }

            // Sort TeeBoxes in numerical order
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const teeBoxA = parseInt(a.replace('TeeBox ', ''));
                const teeBoxB = parseInt(b.replace('TeeBox ', ''));
                return teeBoxA - teeBoxB;
            });

            // Iterate through each sorted TeeBox and render flights
            sortedTeeBoxes.forEach((teeBox, index) => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');

                // Display the TeeBox heading
                flightDetailDiv.append('<h5>' + teeBox + '</h5>');

                // Add Flight button under TeeBox heading
                const addFlightButton = $(`
                    <button class="btn btn-secondary add-flight-btn mb-3" data-tee-box="${teeBox}" data-round-number="${roundNumber}">
                        Add Flight
                    </button>
                `);
                flightDetailDiv.append(addFlightButton);

                // Sort flightIds in numerical order based on flight name (e.g., A1, B1)
                const sortedFlights = teeFlights.sort((a, b) => {
                    const numberA = parseInt(a.flightName.replace(/[^\d]/g, ''), 10);
                    const numberB = parseInt(b.flightName.replace(/[^\d]/g, ''), 10);
                    return numberA - numberB;
                });

                // Iterate through each sorted flight under this TeeBox
                sortedFlights.forEach((flight) => {
                    const flightId = flight.flightId;
                    const flightName = flight.flightName;
                    const flightTime = flight.flightTime;
                    const startPoint = flight.startPoint;

                    // Flight heading
                    flightDetailDiv.append(`
                        <h6>
                          <strong>${flightName} (Time: ${formatTimeToAMPM(flightTime)})</strong>
                          &nbsp;
                          <!-- EDIT BUTTON -->
                          <button class="edit-flight-icon edit-flight-btn"
                                  data-flight-id="${flightId}"
                                  data-flight-name="${flightName}"
                                  data-flight-time="${flightTime}"
                                  data-tee-box="${teeBox}"
                                  data-flight-list-name="${flight.flightListName}"
                                  data-flight-date="${flight.flightDate}"
                                  data-round-number="${roundNumber}">
                            <i class="fas fa-edit"></i>
                          </button>
                        </h6>
                    `);


                    let addPlayerSection = '';
                    if (flight.flightDetails.length < 4) {
                        addPlayerSection = `
                            <div class="add-player-section mb-3">
                                <label class="add-player-label">Add Player:</label>
                                <select class="form-control add-player-select"
                                    data-flight-id="${flightId}"
                                    data-round-number="${roundNumber}"
                                    data-flight-list-name="${flight.flightListName}"
                                    data-flight-date="${flight.flightDate}">
                                    <option value="" disabled selected>Select Player</option>
                                </select>
                                <button class="btn btn-primary add-player-btn"
                                    data-flight-id="${flightId}"
                                    data-round-number="${roundNumber}"
                                    data-flight-list-name="${flight.flightListName}"
                                    data-flight-date="${flight.flightDate}">
                                    Add Player
                                </button>
                            </div>
                        `;
                    }

                    const table = $(`
                        <div class="table-responsive">
                            ${addPlayerSection}
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'Handicap'}</th>
                                        <th>Category</th>
                                        <th>Captain</th>
                                        <th>
                                            <input type="checkbox" class="select-all-checkbox" title="Select All">
                                            <br><small>Select All</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');

                    // List each player within this flight
                    flight.flightDetails.forEach((player) => {
                        const isCaptain = parseInt(player.captain_id) === 1 ? 'checked' : '';

                        // Create a unique flight identifier
                        const flightIdentifier = `${flightName}_${flightTime}_${teeBox}_${roundNumber}_${startPoint}`;

                        tbody.append(`
                            <tr>
                                <td>${playerNumber}</td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                <td>${displayScore === 1 ? player.total_strokes : (player.player_handicap || 'N/A')}</td>
                                <td>${player.category || 'N/A'}</td>
                                <td>
                                    <input type="checkbox" class="captain-checkbox"
                                        data-flight-id="${player.flight_id}"
                                        data-flight-identifier="${flightIdentifier}" ${isCaptain}>
                                </td>
                                <td>
                                    <input type="checkbox" class="delete-checkbox"
                                        data-flight-id="${player.flight_id}"
                                        data-form-id="${player.form_id}"
                                        data-round-number="${roundNumber}">
                                </td>
                            </tr>
                        `);
                        playerNumber++; // Increment player number globally
                    });

                    flightDetailDiv.append(table);

                    // Add 'Delete Selected Players' button after the table
                    flightDetailDiv.append('<button class="btn btn-danger delete-selected-players mt-2" data-round-number="' + roundNumber + '">Delete Players</button>');

                    if (flight.flightDetails.length < 4) {
                        loadAvailablePlayers(flightId, roundNumber);
                    }
                });

                // Assign TeeBoxes to alternating columns
                const middleIndex = Math.ceil(sortedTeeBoxes.length / 2);
                if (index < middleIndex) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Function to render Customize group boxes
        function renderCustomizeGroupBoxes(groupedFlights, roundNumber, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');  // Left column
            const col2 = $('<div class="col-lg-6 mb-3"></div>');  // Right column
            let playerNumber = 1;  // Initialize player number counter
            let index = 0;  // Index counter for assigning columns

            // Iterate through each flight
            for (const flightName in groupedFlights) {
                const flightDetails = groupedFlights[flightName];
                const flightInfo = flightDetails[0];

                const flightId = flightInfo.flight_id;
                const flightTime = flightInfo.flight_time;
                const teeBox = flightInfo.tee_box;
                const startPoint = flightInfo.start_point;
                
                    // Flight heading
                    flightDetailDiv.append(`
                        <h6>
                          <strong>${flightName} (Time: ${formatTimeToAMPM(flightTime)})</strong>
                          &nbsp;
                          <!-- EDIT BUTTON -->
                          <button class="edit-flight-icon edit-flight-btn"
                                  data-flight-id="${flightId}"
                                  data-flight-name="${flightName}"
                                  data-flight-time="${flightTime}"
                                  data-tee-box="${teeBox}"
                                  data-flight-list-name="${flight.flightListName}"
                                  data-flight-date="${flight.flightDate}"
                                  data-round-number="${roundNumber}">
                            <i class="fas fa-edit"></i>
                          </button>
                        </h6>
                    `);

                let addPlayerSection = '';
                if (flightDetails.length < 4) {
                    addPlayerSection = `
                        <div class="add-player-section mb-3">
                            <label class="add-player-label">Add Player:</label>
                            <select class="form-control add-player-select"
                                data-flight-id="${flightId}"
                                data-round-number="${roundNumber}"
                                data-flight-list-name="${flightInfo.flight_list_name}"
                                data-flight-date="${flightInfo.flight_date}">
                                <option value="" disabled selected>Select Player</option>
                            </select>
                            <button class="btn btn-primary add-player-btn"
                                data-flight-id="${flightId}"
                                data-round-number="${roundNumber}"
                                data-flight-list-name="${flightInfo.flight_list_name}"
                                data-flight-date="${flightInfo.flight_date}">
                                Add Player
                            </button>
                        </div>
                    `;
                }

                const flightDetailDiv = $(`
                    <div class="flight-detail">
                        <h5>TeeBox ${teeBox}</h5>
                        <h6><strong>Flight ${flightInfo.flight_name}
                        (Time: ${formatTimeToAMPM(flightTime)})</strong></h6>
                        ${addPlayerSection}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'Handicap'}</th>
                                        <th>Category</th>
                                        <th>Captain</th>
                                        <th>
                                    <input type="checkbox" class="select-all-checkbox" title="Select All">
                                    <br><small>Select All</small>
                                </th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                `);

                const tbody = flightDetailDiv.find('tbody');
                flightDetails.forEach((flight) => {
                    const isCaptain = parseInt(flight.captain_id) === 1 ? 'checked' : '';

                    // Create a unique flight identifier
                    const flightIdentifier = `${flightName}_${flightTime}_${teeBox}_${roundNumber}_${startPoint}`;

                    tbody.append(`
                        <tr>
                            <td>${playerNumber++}</td>
                            <td>${flight.player_name || 'No player assigned'}</td>
                            <td>${displayScore === 1 ? flight.total_strokes : (flight.player_handicap || 'N/A')}</td>
                            <td>${flight.category || 'N/A'}</td>
                            <td>
                                <input type="checkbox" class="captain-checkbox"
                                    data-flight-id="${flight.flight_id}"
                                    data-flight-identifier="${flightIdentifier}" ${isCaptain}>
                            </td>
                            <td>
                                <input type="checkbox" class="delete-checkbox"
                                    data-flight-id="${flight.flight_id}"
                                    data-round-number="${roundNumber}">
                            </td>
                        </tr>
                    `);
                });

                // Add 'Delete Selected Players' button after the table
                flightDetailDiv.append('<button class="btn btn-danger delete-selected-players mt-2" data-round-number="' + roundNumber + '">Delete Players</button>');

                if (flightDetails.length < 4) {
                    loadAvailablePlayers(flightId, roundNumber);
                }

                // Assign flightDetailDiv to columns alternately
                if (index % 2 === 0) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
                index++;
            }

            // Create a row containing two columns
            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }


        // CAPTAIN checkbox handling
        $(document).on('change', '.captain-checkbox', function () {
            const flightId = $(this).data('flight-id');
            const isChecked = $(this).is(':checked') ? 1 : 0;
            const checkbox = $(this);
            const flightIdentifier = checkbox.data('flight-identifier');

            $.ajax({
                url: 'update_captain.php',
                type: 'POST',
                dataType: 'json',
                data: { flight_id: flightId, captain_id: isChecked },
                success: function (result) {
                    if (result.status === 'success') {
                        if (isChecked) {
                            $(`.captain-checkbox[data-flight-identifier="${flightIdentifier}"]`)
                              .not(checkbox).prop('checked', false);
                        }
                    } else {
                        alert(result.message || 'Failed to update captain status.');
                        checkbox.prop('checked', !isChecked);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to update captain status:', error);
                    alert('Error updating captain status.');
                    checkbox.prop('checked', !isChecked);
                }
            });
        });

        // SELECT ALL checkbox handling
        $(document).on('change', '.select-all-checkbox', function () {
            const isChecked = $(this).is(':checked');
            const table = $(this).closest('table');
            table.find('.delete-checkbox').prop('checked', isChecked);
        });

        // Individual delete checkbox handling - update select all state
        $(document).on('change', '.delete-checkbox', function () {
            const table = $(this).closest('table');
            const totalCheckboxes = table.find('.delete-checkbox').length;
            const checkedCheckboxes = table.find('.delete-checkbox:checked').length;
            const selectAllCheckbox = table.find('.select-all-checkbox');
            
            if (checkedCheckboxes === 0) {
                selectAllCheckbox.prop('checked', false).prop('indeterminate', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                selectAllCheckbox.prop('checked', true).prop('indeterminate', false);
            } else {
                selectAllCheckbox.prop('checked', false).prop('indeterminate', true);
            }
        });

        // DELETE selected players
        $(document).on('click', '.delete-selected-players', function () {
            const roundNumber = $(this).data('round-number');
            const checkboxes = $(this).closest('.flight-detail').find('.delete-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('Please select at least one player to delete.');
                return;
            }
            
            // Confirm deletion
            const confirmMessage = checkboxes.length === 1 
                ? 'Are you sure you want to delete this player?' 
                : `Are you sure you want to delete ${checkboxes.length} players?`;
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            const flightIds = [];
            const formIds = [];

            checkboxes.each(function () {
                flightIds.push($(this).data('flight-id'));
                formIds.push($(this).data('form-id'));
            });

            $.ajax({
                url: 'delete_player.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    flight_ids: flightIds,
                    form_ids: formIds,
                    round_number: roundNumber
                },
                success: function (response) {
                    if (response.status === 'success') {
                        alert(response.message || 'Players deleted successfully.');
                        $('#roundFilter').trigger('change');
                    } else {
                        alert(response.message || 'Failed to delete players.');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                    alert('An error occurred while deleting players.');
                }
            });
        });

        // ADD player to an existing flight
        $(document).on('click', '.add-player-btn', function () {
            const flightId = $(this).data('flight-id');
            const roundNumber = $(this).data('round-number');
            const flightListName = $(this).data('flight-list-name');
            const flightDate = $(this).data('flight-date');
            const playerSelect = $(this).closest('.flight-detail')
                                  .find(`.add-player-select[data-flight-id="${flightId}"]`);
            const selectedPlayerId = playerSelect.val();

            if (!selectedPlayerId || !roundNumber || !flightListName || !flightDate) {
                alert('Please select a player and ensure round, flight list name, and flight date exist.');
                return;
            }

            const addButton = $(this);
            addButton.prop('disabled', true).text('Adding...');

            $.ajax({
                url: 'add_player.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    flight_id: flightId,
                    form_id: selectedPlayerId,
                    round_number: roundNumber,
                    event_mgm_id: eventMgmId,
                    flight_list_name: flightListName,
                    flight_date: flightDate
                },
                success: function (result) {
                    if (result.status === 'success') {
                        alert(result.message || 'Player added successfully.');
                        $('#roundFilter').trigger('change'); 
                    } else {
                        alert(result.message || 'Failed to add player.');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to add player:', error);
                    alert('Error adding player. Please try again.');
                },
                complete: function () {
                    submitBtn.prop('disabled', false).text('Add Player');
                }
            });
        });

        // Open Add Flight modal
        $(document).on('click', '.add-flight-btn', function () {
            const teeBox = $(this).data('tee-box');
            const roundNumber = $(this).data('round-number');
            const addFlightForm = $('#addFlightForm');

            addFlightForm.data('tee-box', teeBox);
            addFlightForm.data('round-number', roundNumber);

            // reset form
            if (typeof addFlightForm[0].reset === 'function') {
                addFlightForm[0].reset();
            }
            $('#selectPlayers').val(null).trigger('change');
            $('#selectedPlayersList').html('<li>None selected</li>');

            $('#addFlightModal').modal('show');
        });

        // Submit Add Flight form
        $('#addFlightForm').on('submit', function (e) {
            e.preventDefault();
            const teeBox = $(this).data('tee-box');
            const roundNumber = $(this).data('round-number');
            const flightListName = $('#flightListName').val();
            const flightName = $('#flightName').val().trim();
            const flightTime = $('#flightTime').val();
            const selectedPlayers = $('#selectPlayers').val();
            if (!flightName || !flightTime) {
                alert('Please fill in all required fields.');
                return;
            }
            if (!selectedPlayers || selectedPlayers.length === 0) {
                alert('Please select at least one player.');
                return;
            }
            if (selectedPlayers.length > 4) {
                alert('You can select a maximum of 4 players.');
                return;
            }
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true).text('Adding...');

            $.ajax({
                url: 'AddFlight.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    event_mgm_id: eventMgmId,
                    round_number: roundNumber,
                    flight_list_name: flightListName,
                    flight_name: flightName,
                    flight_time: flightTime,
                    tee_box: teeBox,
                    players: selectedPlayers
                },
                success: function (result) {
                    if (result.status === 'success') {
                        alert(result.message || 'Flight added successfully.');
                        $('#addFlightModal').modal('hide');
                        $('#roundFilter').trigger('change');
                    } else {
                        alert(result.message || 'Failed to add flight.');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to add flight:', error);
                    alert('An unexpected error occurred while adding the flight.');
                },
                complete: function () {
                    submitBtn.prop('disabled', false).text('Add Flight');
                }
            });
        });

        // load available players for a flight
        function loadAvailablePlayers(flightId, roundNumber) {
            $.ajax({
                url: 'fetch_available_players.php',
                type: 'GET',
                dataType: 'json',
                data: {
                    event_mgm_id: eventMgmId,
                    round_number: roundNumber,
                    flight_list_name: $('#flightListName').val()
                },
                success: function (response) {
                    if (response.status === 'success') {
                        const players = response.players;
                        const playerSelect = $(`.add-player-select[data-flight-id="${flightId}"]`);
                        if (players.length > 0) {
                            playerSelect.empty();
                            players.forEach(player => {
                                const fullname = $('<div>').text(player.fullname).html();
                                const handicap = $('<div>').text(player.handicap).html();
                                const category = $('<div>').text(player.category_name).html();
                                playerSelect.append(`
                                    <option value="${player.form_id}">
                                      ${fullname} (Handicap: ${handicap}, Category: ${category})
                                    </option>
                                `);
                            });
                            playerSelect.val(null).trigger('change'); // Clear selection if repopulating
                            $('#selectedPlayersList').html('<li>None selected</li>');
                        } else {
                            playerSelect.empty().append('<option value="" disabled>No available players</option>');
                            playerSelect.val(null).trigger('change'); // Clear selection if no players
                            $('#selectedPlayersList').html('<li>None selected</li>');
                        }
                    } else {
                        alert(response.message || 'Error fetching available players.');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to load available players:', error);
                    alert('Failed to load available players.');
                }
            });
        }

        // Populate players in the "Add Flight" modal
        function populatePlayerSelect() {
            const playerSelect = $('#selectPlayers');
            //playerSelect.empty().append('<option value="" disabled selected>Select Players</option>');
            $.ajax({
                url: 'fetch_available_players.php',
                type: 'GET',
                dataType: 'json',
                data: {
                    event_mgm_id: eventMgmId,
                    round_number: selectedRound,
                    flight_list_name: $('#flightListName').val()
                },
                success: function (response) {
                    if (response.status === 'success') {
                        const players = response.players;
                        if (players.length > 0) {
                            playerSelect.empty();
                            players.forEach(player => {
                                const fullname = $('<div>').text(player.fullname).html();
                                const handicap = $('<div>').text(player.handicap).html();
                                const category = $('<div>').text(player.category_name).html();
                                playerSelect.append(`
                                    <option value="${player.form_id}">
                                      ${fullname} (Handicap: ${handicap}, Category: ${category})
                                    </option>
                                `);
                            });
                        } else {
                            playerSelect.empty().append('<option value="" disabled>No available players</option>');
                            playerSelect.val(null).trigger('change'); // Clear selection if no players
                            $('#selectedPlayersList').html('<li>None selected</li>');
                        }
                    } else {
                        alert(response.message || 'Error fetching available players.');
                        playerSelect.empty().append('<option value="" disabled>No available players</option>');
                        playerSelect.val(null).trigger('change'); // Clear selection if no players
                        $('#selectedPlayersList').html('<li>None selected</li>');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to load available players:', error);
                    alert('Failed to load available players. Please try again.');
                    playerSelect.empty().append('<option value="" disabled>No available players</option>');
                    playerSelect.val(null).trigger('change'); // Clear selection if no players
                    $('#selectedPlayersList').html('<li>None selected</li>');
                }
            });
        }

        // "Add Flight" Modal show -> load players
        $('#addFlightModal').on('show.bs.modal', function () {
            populatePlayerSelect();
        });

        // Limit the Add Flight modal to 4 players
        $('#selectPlayers').on('change', function () {
            const selectedOptions = $(this).find('option:selected');
            const selectedCount = selectedOptions.length;
            const remaining = 4 - selectedCount;

            if (remaining > 0) {
                $('.form-text.text-muted').text(`You can select up to ${remaining} more player(s).`);
            } else {
                $('.form-text.text-muted').text('You have reached the maximum of 4 players.');
            }
            if (selectedCount >= 4) {
                $(this).find('option:not(:selected)').attr('disabled', 'disabled');
            } else {
                $(this).find('option').removeAttr('disabled');
            }
        });

        // Reset the player selection on close
        $('#addFlightModal').on('hidden.bs.modal', function () {
            const playerSelect = $('#selectPlayers');
            playerSelect.find('option').removeAttr('disabled');
            playerSelect.val(null).trigger('change');
            $('.form-text.text-muted').text('You can select up to 4 players for this flight.');
            $('#selectedPlayersList').html('<li>None selected</li>');
        });

        // EDIT FLIGHT logic
        // 1) Open Edit Flight modal
        $(document).on('click', '.edit-flight-btn', function () {
            const flightId = $(this).data('flight-id');
            const flightName = $(this).data('flight-name');
            const flightTime = $(this).data('flight-time');
            const teeBox = $(this).data('tee-box');
            const flightListName = $(this).data('flight-list-name');
            const flightDate = $(this).data('flight-date');
            const roundNumber = $(this).data('round-number');

            $('#editFlightId').val(flightId);
            $('#editFlightName').val(flightName);
            $('#editFlightTime').val(flightTime);
            $('#editTeeBox').val(teeBox);
            $('#editFlightListName').val(flightListName);
            $('#editFlightDate').val(flightDate);
            $('#editRoundNumber').val(roundNumber);

            $('#editFlightModal').modal('show');
        });

        // 2) Handle Edit Flight form submission
        $('#editFlightForm').on('submit', function(e) {
            e.preventDefault();
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true).text('Updating...');

            const formData = $(this).serialize(); // gather all fields: flight_id, flight_name, flight_time, tee_box, etc.

            $.ajax({
                url: 'update_flight.php',
                type: 'POST',
                dataType: 'json',
                data: formData,
                success: function(response) {
                    if (response.status === 'success') {
                        alert(response.message || 'Flight updated successfully.');
                        $('#editFlightModal').modal('hide');
                        $('#roundFilter').trigger('change'); // refresh flights
                    } else {
                        alert(response.message || 'Failed to update flight.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Failed to update flight:', error);
                    alert('Error updating flight. Please try again.');
                },
                complete: function() {
                    submitBtn.prop('disabled', false).text('Update Flight');
                }
            });
        });
    });
    </script>
</body>
</html>
