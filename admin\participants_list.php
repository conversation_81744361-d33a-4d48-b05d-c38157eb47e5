<?php
require("../database.php");
// Get event_mgm_id and category_id from the query string (GET method)
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as total_records, 
                     SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
              FROM event_role_mgm 
              WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($total_records, $match_event);
    $stmt->fetch();
    $stmt->close();

    if ($total_records > 0) {
        // Profile ID is registered in event_role_mgm
        if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
            // If event_mgm_id doesn't match or role is not gPHOfKV0sL
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } else {
        // Profile ID not registered in event_role_mgm
        if ($admin_roleid != 'gPHOfKV0sL') {
            // If role is not gPHOfKV0sL, deny access
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
if (empty($event_mgm_id)) {
    echo "No event selected.";
    exit;
}

// Fetch categories for the event based on event_mgm_id
$category_query = "
    SELECT DISTINCT ec.category_id, ec.category_name
    FROM event_category ec
    JOIN event_mgm em ON em.category_id = ec.category_id
    WHERE em.event_mgm_id = ?
";
$category_stmt = $conn->prepare($category_query);
$category_stmt->bind_param("s", $event_mgm_id);
$category_stmt->execute();
$category_result = $category_stmt->get_result();
$categories = [];
while ($row = $category_result->fetch_assoc()) {
    $categories[] = $row;
}

// Get participant count for each category
$category_counts = [];
foreach ($categories as $cat) {
    $count_query = "
        SELECT COUNT(*) as participant_count
        FROM registration_form rf
        JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
        WHERE em.event_mgm_id = ? AND rf.category_id = ?
    ";
    $count_stmt = $conn->prepare($count_query);
    $count_stmt->bind_param("ss", $event_mgm_id, $cat['category_id']);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $count_data = $count_result->fetch_assoc();
    $category_counts[$cat['category_id']] = $count_data['participant_count'];
    $count_stmt->close();
}

// If no category_id is provided, default to the first category in the list
if (empty($category_id) && count($categories) > 0) {
    $category_id = $categories[0]['category_id'];
}

// Fetch participants based on the selected event_mgm_id and category_id
$participants_query = "
   SELECT 
        rf.form_id, 
        rf.fullname, 
        p.profile_email, 
        em.event_name, 
        ec.category_name,
        rf.hide, 
        rf.hide_rmk,
        COUNT(*) OVER() AS total_participants
    FROM registration_form rf
    JOIN profile p ON p.profile_id = rf.profile_id
    JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
    JOIN event_category ec ON ec.category_id = rf.category_id
    WHERE em.event_mgm_id = ? AND rf.category_id = ?
";
$participants_stmt = $conn->prepare($participants_query);
$participants_stmt->bind_param("ss", $event_mgm_id, $category_id);
$participants_stmt->execute();
$participants_result = $participants_stmt->get_result();
$participants = [];
$totalParticipants = 0; // Initialize with default value
while ($row = $participants_result->fetch_assoc()) {
    $participants[] = $row;
    $totalParticipants = $row['total_participants'];
}

$evtIdQuery = "SELECT event_id 
               FROM event_mgm 
               WHERE event_mgm_id = ? 
                 AND category_id = ? 
               LIMIT 1";
$evtStmt = $conn->prepare($evtIdQuery);
$evtStmt->bind_param("ss", $event_mgm_id, $category_id);
$evtStmt->execute();
$evtStmt->bind_result($found_event_id);
$evtStmt->fetch();
$evtStmt->close();

// Fetch total participants across all categories for this event
$total_query = "
    SELECT COUNT(*) as all_participants
    FROM registration_form rf
    JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
    WHERE em.event_mgm_id = ?
";
$total_stmt = $conn->prepare($total_query);
$total_stmt->bind_param("s", $event_mgm_id);
$total_stmt->execute();
$total_stmt->bind_result($totalAllParticipants);
$total_stmt->fetch();
$total_stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Participants List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Professional Styling */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Professional Header */
        .header-section {
            background-color: #ffffff;
            padding: 32px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            color: #1f2937;
            letter-spacing: -0.025em;
        }
        /* 3. Professional Cards and Tables */
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .card-header {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            padding: 20px 24px;
        }

        .card-body {
            padding: 24px;
        }

        .table-container {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .table {
            margin: 0;
            font-size: 0.875rem;
        }

        .table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table td {
            padding: 16px 20px;
            text-align: center;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }
        /* 4. Professional Buttons and Forms */
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.75rem;
        }

        .form-control, .form-select {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-control:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 5. Professional Badges */
        .badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        /* 6. Professional Statistics Display */
        .stats-container {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .stats-text {
            font-size: 1rem;
            font-weight: 500;
            color: #374151;
        }

        /* 7. Professional Control Section */
        .control-section {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .control-row {
            gap: 20px;
        }

        /* 8. Professional Mobile Responsive Design */
        @media (max-width: 768px) {
            .main-title {
                font-size: 1.5rem;
                margin: 16px 0;
            }

            .header-section {
                padding: 20px 0;
                margin-bottom: 20px;
            }

            .container {
                padding: 0 12px;
            }

            .card-header {
                padding: 16px;
                flex-direction: column !important;
                gap: 16px;
            }

            .card-body {
                padding: 16px;
            }

            .control-section {
                padding: 16px;
            }

            .control-row {
                flex-direction: column !important;
                gap: 16px !important;
            }

            .stats-container {
                padding: 16px;
            }

            .col-md-2-4 {
                width: 100% !important;
                margin-bottom: 12px;
            }

            .input-group {
                width: 100% !important;
                margin-bottom: 12px !important;
            }

            .btn {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
            }

            .form-control, .form-select {
                padding: 10px 14px;
                font-size: 0.875rem;
            }

            /* Mobile Table Styling */
            .table-container {
                border-radius: 8px;
                overflow-x: auto;
            }

            .table {
                min-width: 700px;
            }

            .table th, .table td {
                padding: 12px 8px;
                font-size: 0.8rem;
                white-space: nowrap;
            }

            .table th {
                font-size: 0.7rem;
            }

            .badge {
                padding: 4px 8px;
                font-size: 0.7rem;
            }

            .modal-dialog {
                margin: 8px;
            }

            .modal-content {
                border-radius: 12px;
            }
        }

        /* 9. Extra Small Mobile Devices */
        @media (max-width: 576px) {
            .container {
                padding: 0 8px;
            }

            .main-title {
                font-size: 1.25rem;
                margin: 12px 0;
            }

            .header-section {
                padding: 16px 0;
                margin-bottom: 16px;
            }

            .card-header, .card-body, .control-section, .stats-container {
                padding: 12px;
            }

            .table th, .table td {
                padding: 8px 6px;
                font-size: 0.75rem;
            }

            .table th {
                font-size: 0.65rem;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.875rem;
            }

            .form-control, .form-select {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .badge {
                padding: 3px 6px;
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body>
       <!-- Header -->
       <?php include("admin_header.php");?>
    
    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Event Participants List</h2>
    </div>

    <!-- Professional Statistics Display -->
    <div class="container">
        <div class="stats-container">
            <p class="stats-text mb-0"><strong>Total Participants Across All Categories:</strong> <?php echo $totalAllParticipants; ?></p>
        </div>
    </div>
    
    <!-- Category Counts Table -->
    <div class="container mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Participants Count by Category</h5>
                <div class="d-flex align-items-center">
                    <div class="input-group me-3" style="width: 250px;">
                        <input type="text" id="categorySearchInput" class="form-control form-control-sm" placeholder="Search categories...">
                        <button class="btn btn-outline-light btn-sm" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <a href="export_participants.php?event_mgm_id=<?php echo htmlspecialchars($event_mgm_id); ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-download"></i> Export All Participants
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php
                // Group categories
                $groupedCategories = [];
                foreach ($categories as $category) {
                    $firstWord = strtolower(explode(' ', $category['category_name'])[0]);
                    if (!isset($groupedCategories[$firstWord])) {
                        $groupedCategories[$firstWord] = [];
                    }
                    $groupedCategories[$firstWord][] = $category;
                }

                // Display each group in its own row
                foreach ($groupedCategories as $group => $groupCategories):
                    $color = ($group == 'boys') ? 'primary' : 'success';
                ?>
                <div class="row mb-4">
                    <h6 class="text-<?php echo $color; ?> mb-3"><?php echo ucfirst($group); ?> Divisions</h6>
                    <?php foreach ($groupCategories as $cat): ?>
                    <div class="col-md-2-4 col-sm-6 mb-3" style="width: 20%;">
                        <div class="card h-100 border-<?php echo $color; ?> shadow-sm">
                            <div class="card-header bg-<?php echo $color; ?> text-white">
                                <h6 class="card-title mb-0 text-center"><?php echo htmlspecialchars($cat['category_name']); ?></h6>
                            </div>
                            <div class="card-body text-center p-2">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-users fa-lg text-<?php echo $color; ?> me-2"></i>
                                    <h4 class="card-text text-<?php echo $color; ?> mb-0"><?php echo $category_counts[$cat['category_id']]; ?></h4>
                                </div>
                                <p class="card-text text-muted small mb-2">participants</p>
                                <a href="export_participants.php?event_mgm_id=<?php echo htmlspecialchars($event_mgm_id); ?>&category_id=<?php echo htmlspecialchars($cat['category_id']); ?>" class="btn btn-outline-<?php echo $color; ?> btn-sm">
                                    <i class="fas fa-download"></i> Export
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Professional Control Section -->
    <div class="container">
        <div class="control-section">
            <div class="d-flex justify-content-between align-items-center control-row">
                <form method="GET" action="participants_list.php" class="flex-grow-1">
                    <input type="hidden" name="event_mgm_id" value="<?php echo htmlspecialchars($event_mgm_id); ?>">
                    <div>
                        <label for="categorySelect" class="form-label">Select Category</label>
                        <select name="category_id" id="categorySelect" class="form-select" onchange="this.form.submit()">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo htmlspecialchars($category['category_id']); ?>"
                                    <?php echo $category['category_id'] == $category_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </form>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadCSVModal">
                    Upload Bulk Participants
                </button>
            </div>
        </div>

        <div class="stats-container">
            <p class="stats-text mb-0"><strong>Participants in <?php echo htmlspecialchars($categories[array_search($category_id, array_column($categories, 'category_id'))]['category_name']); ?>:</strong> <?php echo $totalParticipants; ?></p>
        </div>
    </div>
    <!-- Participants List -->
    <div class="container">
        <div class="table-container">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <!-- <th>Participant ID</th> -->
                         <th>No.</th>
                        <th>Player Full Name</th>
                        <th>User Email</th>
                        <th>Event</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="participantsTable">
                    <?php if (!empty($participants)): ?>
                        <?php 
                        $counter = 1;
                        foreach ($participants as $participant): 
                        ?>
                            <tr>
                                <!-- sequential number instead of form_id -->
                                <td><?php echo $counter++; ?></td> 
                                <td><a href="player_profile.php?form_id=<?php echo htmlspecialchars($participant['form_id']); ?>"><?php echo htmlspecialchars($participant['fullname']); ?></a></td>
                                <td><?php echo htmlspecialchars($participant['profile_email']); ?></td>
                                <td><?php echo htmlspecialchars($participant['event_name']); ?></td>
                                <td><?php echo htmlspecialchars($participant['category_name']); ?></td>
                                <td>
                                    <?php
                                    $hide = $participant['hide'];
                                    $hide_rmk = $participant['hide_rmk'];
                                    if ($hide == 0) {
                                        echo '<span class="badge bg-success">Active</span>';
                                    } else {
                                        $status = '';
                                        $color = 'danger';
                                        switch ($hide_rmk) {
                                            case 0:
                                                $status = 'Withdraw';
                                                break;
                                            case 1:
                                                $status = 'DQ';
                                                break;
                                            case 2:
                                                $status = 'Did Not Finish';
                                                break;
                                        }
                                        echo '<span class="badge bg-' . $color . '">' . $status . '</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <a href="../result_history.php?event_id=<?php echo urlencode($found_event_id); ?>&formid=<?php echo urlencode($participant['form_id']); ?>">
                                        <button class="btn btn-primary">Result</button>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7">No participants found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    </div>

    <div class="modal fade" id="uploadCSVModal" tabindex="-1" aria-labelledby="uploadCSVModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadCSVModalLabel">Upload Participants CSV</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                <form method="POST" action="upload_bulk.php?event_mgm_id=<?php echo $event_mgm_id; ?>&category_id=<?php echo $category_id; ?>" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csvFile" class="form-label">Choose CSV File</label>
                            <input type="file" name="csv_file" id="csvFile" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <a href="example.csv" download="example.csv" class="btn btn-link">Download Example CSV</a>
                        </div>
                        <div class="mb-3 text-end">
                            <button type="submit" class="btn btn-primary">Upload</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <!-- Footer -->
    <?php include("admin_footer.php");?>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
    <script>
 // Function to toggle dropdown visibility
 function toggleDropdown(event) {
            event.preventDefault(); // Prevent the default action
            event.stopPropagation(); // Prevent the event from bubbling up

            const dropdownMenu = event.currentTarget.nextElementSibling;
            const icon = event.currentTarget.querySelector('i.fas');

            // Close all other open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                    const otherIcon = menu.previousElementSibling.querySelector('i.fas');
                    if (otherIcon) {
                        otherIcon.classList.remove('fa-chevron-up');
                        otherIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Toggle the current dropdown
            const isShown = dropdownMenu.classList.contains('show');
            dropdownMenu.classList.toggle('show', !isShown);
            icon.classList.toggle('fa-chevron-down', isShown);
            icon.classList.toggle('fa-chevron-up', !isShown);
        }

        // Function to close dropdown when clicking outside
        function closeDropdown(event) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    menu.classList.remove('show');
                    const icon = menu.previousElementSibling.querySelector('i.fas');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                }
            });
        }

        // Event listener to close dropdown on clicking outside
        document.addEventListener('click', closeDropdown);

        // Event listeners for each dropdown toggle button
        document.querySelectorAll('.dropdown > a').forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // Safe element access with error checking
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', () => {
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu) {
                    mobileMenu.classList.toggle('hidden');
                }
            });
        }

        // Loading screen (if exists)
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }
        });

        // Search functionality (if search box exists)
        const searchBox = document.getElementById('searchBox');
        if (searchBox) {
            searchBox.addEventListener('keyup', function() {
                const searchValue = this.value.toLowerCase();
                const rows = document.querySelectorAll('#participantsTable tr');

                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    let matches = false;
                    cells.forEach(cell => {
                        if (cell.textContent.toLowerCase().includes(searchValue)) {
                            matches = true;
                        }
                    });
                    if (matches) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
        
        // Category Cards Filter
        document.getElementById('categorySearchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const categoryCards = document.querySelectorAll('#categoryCards .col-md-3');
            
            categoryCards.forEach(card => {
                const categoryName = card.querySelector('.card-title').textContent.toLowerCase();
                if (categoryName.includes(searchValue)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
