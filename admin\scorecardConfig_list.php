<?php 
session_start();
require("../database.php");

// Proceed with fetching and displaying the page content only for GET requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Fetch the event_mgm_id from the URL
    $event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
    if (!$event_mgm_id) {
        die('Event Management ID not provided.');
    }

    $adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as total_records, 
                     SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
              FROM event_role_mgm 
              WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($total_records, $match_event);
    $stmt->fetch();
    $stmt->close();

    if ($total_records > 0) {
        // Profile ID is registered in event_role_mgm
        if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
            // If event_mgm_id doesn't match or role is not gPHOfKV0sL
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } else {
        // Profile ID not registered in event_role_mgm
        if ($admin_roleid != 'gPHOfKV0sL') {
            // If role is not gPHOfKV0sL, deny access
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
    // Fetch event name based on event_mgm_id
    $event_name = '';
    if ($event_mgm_id !== '') {
        $stmt = $conn->prepare("SELECT event_name FROM event_mgm WHERE event_mgm_id = ?");
        $stmt->bind_param("s", $event_mgm_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $event = $result->fetch_assoc();
        $event_name = $event ? $event['event_name'] : 'Unknown Event';
        $stmt->close();
    }

    // Fetch all courses associated with the event_mgm_id to populate the filter dropdown
    $course_query = "
        SELECT DISTINCT c.course_id, c.course_name
        FROM scorecard_configuration sc
        INNER JOIN course_info c ON sc.course_id = c.course_id
        WHERE sc.event_mgm_id = ?
        ORDER BY c.course_name ASC
    ";
    $stmt_course = $conn->prepare($course_query);
    if ($stmt_course) {
        $stmt_course->bind_param("s", $event_mgm_id);
        $stmt_course->execute();
        $course_result = $stmt_course->get_result();
        
        $course_options = "<option value='all' selected>All Courses</option>";
        if ($course_result->num_rows > 0) {
            while ($course = $course_result->fetch_assoc()) {
                $course_id = htmlspecialchars($course['course_id']);
                $course_name = htmlspecialchars($course['course_name']);
                $course_options .= "<option value='{$course_id}'>{$course_name}</option>";
            }
        }
        $stmt_course->close();
    } else {
        // Log the error and display a user-friendly message
        error_log("Database prepare error for courses: " . $conn->error);
        die('Database error occurred while fetching courses.');
    }

    // Fetch all scorecard configurations for the specified event_mgm_id, join with course_info to get course name
    $scorecard_query = "
        SELECT sc.scorecard_id, sc.event_mgm_id, sc.course_id, c.course_name, sc.hole_1, sc.hole_2, sc.hole_3, sc.hole_4, sc.hole_5, sc.hole_6, sc.hole_7, sc.hole_8, sc.hole_9, sc.hole_10, sc.hole_11, sc.hole_12, sc.hole_13, sc.hole_14, sc.hole_15, sc.hole_16, sc.hole_17, sc.hole_18, sc.name, sc.otp, sc.status
        FROM scorecard_configuration sc
        LEFT JOIN course_info c ON sc.course_id = c.course_id
        WHERE sc.event_mgm_id = ?
        ORDER BY sc.scorecard_id ASC
    ";
    
    $stmt_scorecard = $conn->prepare($scorecard_query);
    if ($stmt_scorecard) {
        $stmt_scorecard->bind_param("s", $event_mgm_id);
        $stmt_scorecard->execute();
        $scorecard_result = $stmt_scorecard->get_result();
        
        $scorecard_data = [];
        if ($scorecard_result->num_rows > 0) {
            while ($row = $scorecard_result->fetch_assoc()) {
                $scorecard_data[] = $row;
            }
        }
        $stmt_scorecard->close();
    } else {
        // Log the error and display a user-friendly message
        error_log("Database prepare error for scorecards: " . $conn->error);
        die('Database error occurred while fetching scorecard configurations.');
    }
}

// Handle AJAX request to toggle status
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ensure required POST parameters are set
    if (isset($_POST['scorecard_id']) && isset($_POST['status'])) {
        $scorecard_id = $_POST['scorecard_id'];
        $status = $_POST['status'];

        // Validate scorecard_id (assuming it's an integer)
        if (!filter_var($scorecard_id, FILTER_VALIDATE_INT)) {
            send_json_response(false, 'Invalid scorecard ID.');
        }

        // Validate status (should be either 0 or 1)
        if (!in_array($status, ['0', '1'], true)) {
            send_json_response(false, 'Invalid status value.');
        }

        // Prepare and execute the update query securely using prepared statements
        $stmt_update = $conn->prepare("UPDATE scorecard_configuration SET status = ? WHERE scorecard_id = ?");
        if ($stmt_update) {
            $stmt_update->bind_param("ii", $status, $scorecard_id);
            if ($stmt_update->execute()) {
                if ($stmt_update->affected_rows > 0) {
                    send_json_response(true);
                } else {
                    send_json_response(false, 'No records updated. Please check the scorecard ID.');
                }
            } else {
                // Log the error for debugging (do not expose detailed errors to users)
                error_log("Database execute error: " . $stmt_update->error);
                send_json_response(false, 'Failed to update status.');
            }
            $stmt_update->close();
        } else {
            // Log the error for debugging
            error_log("Database prepare error: " . $conn->error);
            send_json_response(false, 'Database error.');
        }
    } else {
        send_json_response(false, 'Required parameters are missing.');
    }
}


// Function to send JSON responses
function send_json_response($success, $error = '') {
    header('Content-Type: application/json');
    echo json_encode(['success' => $success, 'error' => $error]);
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Station Scorers Configuration List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Styling */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Main Title Styling */
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 24px 0;
            text-align: center;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        .event-name {
            font-size: 1.125rem;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: center;
            color: #6b7280;
        }

        /* 3. Professional Header Section */
        .header-section {
            background-color: #ffffff;
            padding: 32px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 4. Professional Card Layout */
        .control-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .table-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        /* 5. Professional Table Styling */
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: white;
            margin: 0;
        }

        .table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            vertical-align: middle;
            text-align: center;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table-container table td.status-active {
            color: #10b981 !important;
            font-weight: bold;
        }

        .table-container table td.status-disabled {
            color: #ef4444 !important;
            font-weight: bold;
        }

        /* 6. Professional Form Controls */
        .form-control, .form-select {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-control:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 6.1. Filter Layout Improvements */
        .filter-actions-row {
            gap: 20px !important;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .role-box {
            min-width: 200px;
        }

        .dropdown {
            flex-shrink: 0;
        }

        footer {
            background-color: #ffffff;
            padding: 20px 0;
            margin-top: 40px;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0px -1px 3px rgba(0, 0, 0, 0.1);
        }

        footer .social-icons a {
            color: #6b7280;
            transition: color 0.2s ease;
        }

        footer .social-icons a:hover {
            color: #3b82f6;
        }

        .table th.sortable:hover {
            background-color: #f3f4f6;
        }

        .table th.sortable::after {
            content: "\f0dc";
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            padding-left: 10px;
            opacity: 0.5;
        }

        .table th.sortable.sorted-asc::after {
            content: "\f0de";
        }

        .table th.sortable.sorted-desc::after {
            content: "\f0dd";
        }

        .pagination-info {
            font-size: 14px;
            color: #4b5563;
        }

        .entries-select {
            display: flex;
            align-items: center;
            width: 200px;
        }

        .entries-select label {
            margin-right: 8px;
            font-size: 14px;
            color: #4b5563;
        }

        /* 7. Professional Button Styling */
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        /* 7.1. Professional Dropdown Styling */
        .dropdown-menu {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            min-width: 200px;
        }

        .dropdown-item {
            padding: 12px 16px;
            font-size: 0.875rem;
            color: #374151;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background: #f9fafb;
            color: #1f2937;
        }

        /* 8. Action Icons */
        .table-container .icon {
            font-size: 1.125rem;
            cursor: pointer;
            margin: 0 4px;
            padding: 6px;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .table-container .icon.edit-icon {
            color: #3b82f6;
        }

        .table-container .icon.edit-icon:hover {
            background: #eff6ff;
            color: #2563eb;
        }

        .table-container .icon.delete-icon {
            color: #ef4444;
        }

        .table-container .icon.delete-icon:hover {
            background: #fef2f2;
            color: #dc2626;
        }

        /* Button group styles for better layout */
        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .button-group .btn {
            white-space: nowrap;
            font-size: 14px;
            padding: 8px 16px;
        }

        /* 9. Professional OTP Link Styling */
        .otp-copy-btn {
            background: none;
            border: none;
            color: #3b82f6;
            text-decoration: underline;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .otp-copy-btn:hover {
            background: #eff6ff;
            color: #2563eb;
            text-decoration: none;
        }

        /* 9.1. Copy Feedback Styling */
        .copy-feedback {
            color: #059669 !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            margin-left: 8px !important;
        }

        /* 10. Status Styling */
        .status-active {
            color: #059669 !important;
            font-weight: 600 !important;
        }

        .status-disabled {
            color: #dc2626 !important;
            font-weight: 600 !important;
        }

        .actions-btn {
            height: 48px; /* Match .form-control and .form-select */
            font-size: 16px;
            padding: 0 24px;
            border-radius: 8px !important;
            margin-left: 12px;
            display: flex;
            align-items: center;
        }
        /* 11. Professional Responsive Design */
        @media (max-width: 768px) {
            .main-title {
                font-size: 1.5rem;
                margin: 16px 0;
            }

            .event-name {
                font-size: 1rem;
                margin-bottom: 12px;
            }

            .header-section {
                padding: 20px 0;
                margin-bottom: 20px;
            }

            .control-card {
                padding: 16px;
                margin-bottom: 16px;
            }

            .filter-actions-row {
                flex-direction: column !important;
                gap: 16px !important;
            }

            .search-box, .role-box {
                width: 100% !important;
                min-width: 100% !important;
            }

            .form-control, .form-select {
                padding: 10px 14px;
                font-size: 0.875rem;
            }

            .actions-btn {
                width: 100% !important;
                margin-left: 0 !important;
                justify-content: center;
                padding: 12px 20px;
            }

            .table-container {
                overflow-x: auto;
                border-radius: 8px;
            }

            .table {
                min-width: 700px;
            }

            .table th, .table td {
                padding: 12px 8px;
                font-size: 0.8rem;
                text-align: center;
            }

            .table th {
                font-size: 0.7rem;
            }

            .otp-copy-btn {
                padding: 4px 8px;
                font-size: 0.75rem;
            }

            .table-container .icon {
                font-size: 1rem;
                padding: 4px;
            }
        }

        /* 12. Extra Small Mobile Devices */
        @media (max-width: 576px) {
            .container {
                padding: 0 8px;
            }

            .main-title {
                font-size: 1.25rem;
                margin: 12px 0;
            }

            .event-name {
                font-size: 0.875rem;
            }

            .header-section {
                padding: 16px 0;
                margin-bottom: 16px;
            }

            .control-card {
                padding: 12px;
                margin-bottom: 12px;
            }

            .form-control, .form-select {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .actions-btn {
                padding: 10px 16px;
                font-size: 0.875rem;
            }

            .table th, .table td {
                padding: 8px 6px;
                font-size: 0.75rem;
                text-align: center;
            }

            .table th {
                font-size: 0.65rem;
            }

            .otp-copy-btn {
                padding: 3px 6px;
                font-size: 0.7rem;
            }

            .dropdown-menu .dropdown-item {
                padding: 12px 16px;
                font-size: 0.875rem;
            }
        }


    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Station Scorers Configuration List</h2>
        <p class="event-name"><?= htmlspecialchars($event_name) ?></p>
    </div>

    <!-- Professional Control Section -->
    <div class="container mx-auto px-4">
        <div class="control-card">
            <div class="d-flex justify-content-between align-items-center filter-actions-row gap-3">
                <div class="search-box">
                    <input type="text" id="searchBox" class="form-control" placeholder="Search by Name or OTP...">
                </div>
                <div class="role-box">
                    <select id="roleBox" class="form-select">
                        <?php echo $course_options; // Populate filtered courses ?>
                    </select>
                </div>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle d-flex align-items-center gap-2 shadow actions-btn" type="button" id="actionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-gear"></i> Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end shadow rounded" aria-labelledby="actionsDropdown">
                        <li>
                            <a class="dropdown-item" href="marker_configuration_list.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                                Marker Config
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="scorecard_configuration.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                                Add New
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="export_scorecardConfig_excel.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                                Export Excel
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Scorecard Configuration Table -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th class="sortable" data-type="number">No.</th>
                        <th class="sortable" data-type="string">Golf Course</th>
                        <th class="sortable" data-type="string">Name</th>
                        <th class="sortable" data-type="string">OTP</th>
                        <th class="sortable" data-type="string">Holes</th>
                        <th class="sortable" data-type="string">Status</th>
                        <th>Lock</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="scorecardConfigListTable">
                <?php
                if (!empty($scorecard_data)) {
                    $index = 1;
                    foreach ($scorecard_data as $row) {
                        // Determine account status
                        $status = $row['status'] == 0 ? 'Active' : 'Disabled';
                        $status_class = $row['status'] == 0 ? 'status-active' : 'status-disabled';
                        $course = htmlspecialchars($row['course_name']);
                        $name = htmlspecialchars($row['name']);
                        $otp = trim(htmlspecialchars($row['otp'])); // Trim whitespace

                        // Gather selected holes
                        $selected_holes = [];
                        for ($i = 1; $i <= 18; $i++) {
                            if (isset($row["hole_$i"]) && $row["hole_$i"] == 1) {
                                $selected_holes[] = $i;
                            }
                        }
                        $holes_display = !empty($selected_holes) ? implode(", ", $selected_holes) : 'None';
                        ?>
                        <tr data-course_id="<?php echo htmlspecialchars($row['course_id']); ?>">
                            <td><?php echo $index; ?></td>
                            <td><?php echo $course; ?></td>
                            <td><?php echo $name; ?></td>
                            <td>
                                <button 
                                    class="otp-copy-btn" 
                                    id="otp-<?php echo $row['scorecard_id']; ?>" 
                                    onclick="copyOTP('<?php echo $row['scorecard_id']; ?>')" 
                                    title="Click to copy">
                                    <?php echo $otp; ?>
                                </button>
                                <span id="copy-feedback-<?php echo $row['scorecard_id']; ?>" class="copy-feedback" style="display: none;">
                                    ✓ Copied!
                                </span>
                            </td>
                            <td><?php echo $holes_display; ?></td>
                            <td class="<?php echo $status_class; ?>" id="status-<?php echo $row['scorecard_id']; ?>">
                                <?php echo htmlspecialchars($status); ?>
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" <?php echo ($row['status'] == 1 ? 'checked' : ''); ?> onchange="toggleStatus(this, '<?php echo $row['scorecard_id']; ?>')">
                                </div>
                            </td>
                            <td>
                                <a href="edit_scorecardConfig.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>&scorecard_id=<?php echo urlencode($row['scorecard_id']); ?>" class="icon edit-icon" title="Edit Scorecard">
                                    <i class="bi bi-pencil"></i>
                                </a>

                                <!-- Optional: Add Delete functionality -->
                                <!--
                                <a href="delete_scorecard.php?id=<?php echo $row['scorecard_id']; ?>&event_mgm_id=<?php echo urlencode($event_mgm_id); ?>" class="icon delete-icon" onclick="return confirm('Are you sure you want to delete this scorecard?');" title="Delete Scorecard">
                                    <i class="bi bi-trash"></i>
                                </a>
                                -->
                            </td>
                        </tr>
                        <?php
                        $index++;
                    }
                } else {
                    echo "<tr><td colspan='8'>No scorecard configurations found for this Event Management ID.</td></tr>";
                }
                ?>
                </tbody>
            </table>
        </div>

    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
function copyOTP(scorecardId) {
    const otpElement = document.getElementById(`otp-${scorecardId}`);
    const feedbackElement = document.getElementById(`copy-feedback-${scorecardId}`);
    const otpText = otpElement.textContent.trim(); // Trim whitespace

    // Copy OTP to the clipboard
    navigator.clipboard.writeText(otpText).then(() => {
        // Show the "Copied!" message
        feedbackElement.style.display = 'inline';

        // Hide the message after 2 seconds
        setTimeout(() => {
            feedbackElement.style.display = 'none';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy OTP: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = otpText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        // Show feedback even with fallback
        feedbackElement.style.display = 'inline';
        setTimeout(() => {
            feedbackElement.style.display = 'none';
        }, 2000);
    });
}


function toggleStatus(switchElement, scorecard_id) {
    // When the switch is checked, it means the scorecard should be Disabled (status = 1)
    const disabled = switchElement.checked;
    const status = disabled ? 1 : 0;  // If switch is on, status is 1 (Disabled), otherwise it's 0 (Active)
    const statusCell = document.getElementById('status-' + scorecard_id);
    const newStatus = disabled ? 'Disabled' : 'Active';
    const newStatusClass = disabled ? 'status-disabled' : 'status-active';

    // Update the status visually
    statusCell.textContent = newStatus;
    statusCell.className = newStatusClass;

    // Disable the checkbox to prevent multiple clicks
    switchElement.disabled = true;

    // Send AJAX request to update status in the database
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '', true); // Send request to the same file
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onload = function() {
        // Re-enable the checkbox after the request completes
        switchElement.disabled = false;

        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    // Display a success message without alert (optional)
                    alert('Scorecard status updated successfully!');
                } else {
                    alert('Failed to update status: ' + response.error);
                    // Revert changes if update fails
                    switchElement.checked = !disabled;
                    statusCell.textContent = !disabled ? 'Disabled' : 'Active';
                    statusCell.className = !disabled ? 'status-disabled' : 'status-active';
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                alert('An unexpected error occurred.');
                // Revert changes in case of JSON parse error
                switchElement.checked = !disabled ;
                statusCell.textContent = !disabled  ? 'Disabled' : 'Active';
                statusCell.className = !disabled  ? 'status-disabled' : 'status-active';
            }
        } else {
            console.error('AJAX request failed with status:', xhr.status);
            alert('Failed to update status. Please try again.');
            // Revert changes on server error
            switchElement.checked = !disabled ;
            statusCell.textContent = !disabled  ? 'Disabled' : 'Active';
            statusCell.className = !disabled  ? 'status-disabled' : 'status-active';
        }
    };

    // Log the data being sent for debugging (optional, remove in production)
    console.log('scorecard_id=' + encodeURIComponent(scorecard_id) + '&status=' + status);

    // Send the request
    xhr.send('scorecard_id=' + encodeURIComponent(scorecard_id) + '&status=' + status);
}


    </script>

    <script>

        // Function to toggle dropdown visibility
        function toggleDropdown(event) {
            event.preventDefault(); // Prevent the default action
            event.stopPropagation(); // Prevent the event from bubbling up

            const dropdownMenu = event.currentTarget.nextElementSibling;
            const icon = event.currentTarget.querySelector('i.fas');

            // Close all other open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                    const otherIcon = menu.previousElementSibling.querySelector('i.fas');
                    if (otherIcon) {
                        otherIcon.classList.remove('fa-chevron-up');
                        otherIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Toggle the current dropdown
            const isShown = dropdownMenu.classList.contains('show');
            dropdownMenu.classList.toggle('show', !isShown);
            icon.classList.toggle('fa-chevron-down', isShown);
            icon.classList.toggle('fa-chevron-up', !isShown);
        }

        // Function to close dropdown when clicking outside
        function closeDropdown(event) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    menu.classList.remove('show');
                    const icon = menu.previousElementSibling.querySelector('i.fas');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                }
            });
        }

        // Event listener to close dropdown on clicking outside
        document.addEventListener('click', closeDropdown);

        // Event listeners for each dropdown toggle button
        document.querySelectorAll('.dropdown > a').forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            const userListTable = document.getElementById('scorecardConfigListTable'); // Updated selector
            const roleBox = document.getElementById('roleBox');
            const searchBox = document.getElementById('searchBox');
            let sortDirection = {};

            // Filter by Course
            roleBox.addEventListener('change', filterUsers);

            // Search by Name or OTP
            searchBox.addEventListener('input', filterUsers);

            // Sorting functionality
            document.querySelectorAll('.table th.sortable').forEach(header => {
                header.addEventListener('click', () => sortTable(header));
            });

            function filterUsers() {
                const selectedCourseId = roleBox.value.toLowerCase();
                const searchTerm = searchBox.value.toLowerCase();
                const rows = userListTable.querySelectorAll('tr');

                rows.forEach(row => {
                    const courseId = row.getAttribute('data-course_id').toLowerCase();
                    const nameText = row.querySelectorAll('td')[2].textContent.toLowerCase(); // Name in the third column
                    const otpText = row.querySelectorAll('td')[3].textContent.toLowerCase();  // OTP in the fourth column

                    const courseMatch = selectedCourseId === 'all' || courseId === selectedCourseId;
                    const searchMatch = nameText.includes(searchTerm) || otpText.includes(searchTerm);

                    row.style.display = courseMatch && searchMatch ? '' : 'none';
                });
            }

            function sortTable(header) {
                const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                const dataType = header.getAttribute('data-type');
                const direction = sortDirection[columnIndex] = !sortDirection[columnIndex];
                const isNumeric = dataType === 'number';

                const rows = Array.from(userListTable.querySelectorAll('tr')).slice(0); // Include all rows including header

                // Exclude the header row from sorting
                const headerRow = rows.shift();

                rows.sort((a, b) => {
                    const aText = a.children[columnIndex].textContent.trim();
                    const bText = b.children[columnIndex].textContent.trim();

                    if (isNumeric) {
                        return direction ? aText - bText : bText - aText;
                    } else {
                        return direction ? aText.localeCompare(bText) : bText.localeCompare(aText);
                    }
                });

                // Re-append sorted rows
                userListTable.innerHTML = '';
                userListTable.appendChild(headerRow); // Re-add header row
                rows.forEach(row => userListTable.appendChild(row));

                // Update sort indicators
                document.querySelectorAll('.table th.sortable').forEach(th => {
                    th.classList.remove('sorted-asc', 'sorted-desc');
                });
                header.classList.add(direction ? 'sorted-asc' : 'sorted-desc');
            }


            // Delete User with confirmation alert
            window.deleteUser = function(icon) {
                if (confirm("Are you sure you want to delete this user?")) {
                    const row = icon.closest('tr');
                    row.remove();
                }
            };
        });
    </script>
</body>
</html>
