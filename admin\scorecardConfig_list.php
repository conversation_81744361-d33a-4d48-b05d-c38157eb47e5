<?php 
session_start();
require("../database.php");

// Proceed with fetching and displaying the page content only for GET requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Fetch the event_mgm_id from the URL
    $event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
    if (!$event_mgm_id) {
        die('Event Management ID not provided.');
    }

    $adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as total_records, 
                     SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
              FROM event_role_mgm 
              WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($total_records, $match_event);
    $stmt->fetch();
    $stmt->close();

    if ($total_records > 0) {
        // Profile ID is registered in event_role_mgm
        if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
            // If event_mgm_id doesn't match or role is not gPHOfKV0sL
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } else {
        // Profile ID not registered in event_role_mgm
        if ($admin_roleid != 'gPHOfKV0sL') {
            // If role is not gPHOfKV0sL, deny access
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
    // Fetch event name based on event_mgm_id
    $event_name = '';
    if ($event_mgm_id !== '') {
        $stmt = $conn->prepare("SELECT event_name FROM event_mgm WHERE event_mgm_id = ?");
        $stmt->bind_param("s", $event_mgm_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $event = $result->fetch_assoc();
        $event_name = $event ? $event['event_name'] : 'Unknown Event';
        $stmt->close();
    }

    // Fetch all courses associated with the event_mgm_id to populate the filter dropdown
    $course_query = "
        SELECT DISTINCT c.course_id, c.course_name
        FROM scorecard_configuration sc
        INNER JOIN course_info c ON sc.course_id = c.course_id
        WHERE sc.event_mgm_id = ?
        ORDER BY c.course_name ASC
    ";
    $stmt_course = $conn->prepare($course_query);
    if ($stmt_course) {
        $stmt_course->bind_param("s", $event_mgm_id);
        $stmt_course->execute();
        $course_result = $stmt_course->get_result();
        
        $course_options = "<option value='all' selected>All Courses</option>";
        if ($course_result->num_rows > 0) {
            while ($course = $course_result->fetch_assoc()) {
                $course_id = htmlspecialchars($course['course_id']);
                $course_name = htmlspecialchars($course['course_name']);
                $course_options .= "<option value='{$course_id}'>{$course_name}</option>";
            }
        }
        $stmt_course->close();
    } else {
        // Log the error and display a user-friendly message
        error_log("Database prepare error for courses: " . $conn->error);
        die('Database error occurred while fetching courses.');
    }

    // Fetch all scorecard configurations for the specified event_mgm_id, join with course_info to get course name
    $scorecard_query = "
        SELECT sc.scorecard_id, sc.event_mgm_id, sc.course_id, c.course_name, sc.hole_1, sc.hole_2, sc.hole_3, sc.hole_4, sc.hole_5, sc.hole_6, sc.hole_7, sc.hole_8, sc.hole_9, sc.hole_10, sc.hole_11, sc.hole_12, sc.hole_13, sc.hole_14, sc.hole_15, sc.hole_16, sc.hole_17, sc.hole_18, sc.name, sc.otp, sc.status
        FROM scorecard_configuration sc
        LEFT JOIN course_info c ON sc.course_id = c.course_id
        WHERE sc.event_mgm_id = ?
        ORDER BY sc.scorecard_id ASC
    ";
    
    $stmt_scorecard = $conn->prepare($scorecard_query);
    if ($stmt_scorecard) {
        $stmt_scorecard->bind_param("s", $event_mgm_id);
        $stmt_scorecard->execute();
        $scorecard_result = $stmt_scorecard->get_result();
        
        $scorecard_data = [];
        if ($scorecard_result->num_rows > 0) {
            while ($row = $scorecard_result->fetch_assoc()) {
                $scorecard_data[] = $row;
            }
        }
        $stmt_scorecard->close();
    } else {
        // Log the error and display a user-friendly message
        error_log("Database prepare error for scorecards: " . $conn->error);
        die('Database error occurred while fetching scorecard configurations.');
    }
}

// Handle AJAX request to toggle status
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ensure required POST parameters are set
    if (isset($_POST['scorecard_id']) && isset($_POST['status'])) {
        $scorecard_id = $_POST['scorecard_id'];
        $status = $_POST['status'];

        // Validate scorecard_id (assuming it's an integer)
        if (!filter_var($scorecard_id, FILTER_VALIDATE_INT)) {
            send_json_response(false, 'Invalid scorecard ID.');
        }

        // Validate status (should be either 0 or 1)
        if (!in_array($status, ['0', '1'], true)) {
            send_json_response(false, 'Invalid status value.');
        }

        // Prepare and execute the update query securely using prepared statements
        $stmt_update = $conn->prepare("UPDATE scorecard_configuration SET status = ? WHERE scorecard_id = ?");
        if ($stmt_update) {
            $stmt_update->bind_param("ii", $status, $scorecard_id);
            if ($stmt_update->execute()) {
                if ($stmt_update->affected_rows > 0) {
                    send_json_response(true);
                } else {
                    send_json_response(false, 'No records updated. Please check the scorecard ID.');
                }
            } else {
                // Log the error for debugging (do not expose detailed errors to users)
                error_log("Database execute error: " . $stmt_update->error);
                send_json_response(false, 'Failed to update status.');
            }
            $stmt_update->close();
        } else {
            // Log the error for debugging
            error_log("Database prepare error: " . $conn->error);
            send_json_response(false, 'Database error.');
        }
    } else {
        send_json_response(false, 'Required parameters are missing.');
    }
}


// Function to send JSON responses
function send_json_response($success, $error = '') {
    header('Content-Type: application/json');
    echo json_encode(['success' => $success, 'error' => $error]);
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Station Scorers Configuration List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
    
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 36px;
            font-weight: 800;
            margin: 30px 0;
            text-align: center;
            color: #1f2937;
        }

        .event-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #495057;
        }
        
        .header-section {
            background-color: #ffffff;
            padding: 25px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 30px;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-container {
            margin-top: 20px;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
            overflow-x: auto;
        }

        .table th, .table td {
            padding: 16px;
            vertical-align: middle;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }

        .table th {
            background-color: #f9fafb;
            font-weight: 700;
            color: #1f2937;
        }

        .table tbody tr {
            border-bottom: 1px solid #e5e7eb;
        }

        .table tbody tr:hover {
            background-color: #f3f4f6;
        }

        .table-container table td.status-active {
            color: #10b981 !important;
            font-weight: bold;
        }

        .table-container table td.status-disabled {
            color: #ef4444 !important;
            font-weight: bold;
        }

        .search-box input, .role-box select {
            padding: 14px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
        }

        .search-box input:focus, .role-box select:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .role-box select {
            width: 300px;
        }

        footer {
            background-color: #ffffff;
            padding: 20px 0;
            margin-top: 40px;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0px -1px 3px rgba(0, 0, 0, 0.1);
        }

        footer .social-icons a {
            color: #6b7280;
            transition: color 0.2s ease;
        }

        footer .social-icons a:hover {
            color: #3b82f6;
        }

        .table th.sortable:hover {
            background-color: #f3f4f6;
        }

        .table th.sortable::after {
            content: "\f0dc";
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            padding-left: 10px;
            opacity: 0.5;
        }

        .table th.sortable.sorted-asc::after {
            content: "\f0de";
        }

        .table th.sortable.sorted-desc::after {
            content: "\f0dd";
        }

        .pagination-info {
            font-size: 14px;
            color: #4b5563;
        }

        .entries-select {
            display: flex;
            align-items: center;
            width: 200px;
        }

        .entries-select label {
            margin-right: 8px;
            font-size: 14px;
            color: #4b5563;
        }

        .table-container .icon {
            font-size: 20px;
            cursor: pointer;
            margin: 0 5px;
        }

        .table-container .icon.edit-icon {
            color: #3b82f6;
            font-size:24px;
        }

        .table-container .icon.delete-icon {
            color: #ef4444;
             font-size:24px;
        }

        .table-container .icon:hover {
            opacity: 0.7;
        }

        /* Button group styles for better layout */
        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .button-group .btn {
            white-space: nowrap;
            font-size: 14px;
            padding: 8px 16px;
        }

        .otp-copy-btn {
            background: none;
            border: none;
            color: #3b82f6;
            text-decoration: underline;
            cursor: pointer;
            padding: 0;
            font-size: inherit;
        }

        .otp-copy-btn:hover {
            color: #2563eb;
        }

        .actions-btn {
            height: 48px; /* Match .form-control and .form-select */
            font-size: 16px;
            padding: 0 24px;
            border-radius: 8px !important;
            margin-left: 12px;
            display: flex;
            align-items: center;
        }
        @media (max-width: 768px) {
            .actions-btn {
                width: 100%;
                margin-left: 0;
                margin-top: 10px;
            }
        }

        @media (max-width: 768px) {
            /* Adjust header font size */
            .header-section h2 {
                font-size: 24px;
            }

            /* Stack search box, role dropdown, and add new button vertically */
            .d-flex {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-box, .role-box, .button-group {
                width: 100%;
                margin-bottom: 15px; /* Add space between elements */
            }

            /* Ensure buttons are full width on mobile */
            .button-group {
                display: flex;
                flex-direction: column;
            }

            .button-group .btn {
                width: 100%;
                text-align: center;
            }

            /* Adjust input and select for better mobile display */
            .search-box input, .role-box select {
                width: 100%;
                font-size: 14px;
            }

            /* Adjust padding for mobile */
            .search-box input, .role-box select, .btn-primary {
                padding: 10px;
            }

            /* Reduce table padding for better fit */
            .table th, .table td {
                padding: 12px;
            }

            /* Optimize table for mobile */
            .table-container {
                overflow-x: auto;
            }

            .table th, .table td {
                white-space: nowrap;
            }

            .table-container table {
                min-width: 600px;
            }

            /* Reduce button size on mobile */
            .btn-action {
                width: 100px;
                font-size: 12px;
            }
        }

        /* Dropdown Menu Styles */
        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            min-width: 150px;
            border-radius: 5px;
            overflow: hidden;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .dropdown-menu a {
            padding: 10px 20px;
            display: block;
            color: #000;
            text-decoration: none;
            white-space: nowrap;
        }
        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
        .dropdown a {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .dropdown i.fas {
            margin-left: auto;
        }
        .show {
            display: block !important;
        }
        @media (max-width: 768px) {
            .header-section {
                padding: 10px 0;
            }
            .event-name {
            font-size: 18px;
            }
            .container {
                margin-top: 10px;
            }
            .search-box {
                flex-direction: column;
                align-items: stretch;
            }
            .search-box input {
                margin-bottom: 10px;
            }
            .mobile-menu-nav {
                display: flex;
                flex-direction: column;
                padding: 10px 0;
            }
            .mobile-menu-nav a {
                padding: 10px;
                text-decoration: none;
                color: #333;
            }
            .mobile-menu-nav .dropdown-menu {
                position: relative;
                box-shadow: none;
                background: none;
                border: none;
                min-width: 0;
            }
            .mobile-menu-nav .dropdown-menu a {
                padding-left: 20px;
            }
        }
        @media (max-width: 768px) {
            .filter-actions-row {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 0 !important;
            }
            .search-box,
            .role-box {
                width: 100% !important;
                margin-bottom: 16px !important;
            }
            .actions-btn {
                width: 100% !important;
                margin-left: 0 !important;
                margin-top: 16px !important;
                justify-content: center;
                padding-top: 14px;
                padding-bottom: 14px;
                font-size: 1.1rem;
            }
            .dropdown-menu {
                min-width: 100% !important;
                left: 0 !important;
                right: 0 !important;
                padding: 0.5rem 0;
            }
            .dropdown-menu .dropdown-item {
                padding: 1rem 1.5rem;
                font-size: 1.1rem;
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            .dropdown-menu .dropdown-item i {
                font-size: 1.4rem;
                color: #2563eb;
                min-width: 1.8rem;
                text-align: center;
            }
            .dropdown-menu .dropdown-item:active,
            .dropdown-menu .dropdown-item:focus,
            .dropdown-menu .dropdown-item:hover {
                background: #f3f4f6;
                color: #1d4ed8;
            }
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Station Scorers Configuration List</h2>
        <p class="event-name"><?= htmlspecialchars($event_name) ?></p>
    </div>

    <!-- Search Box, Course Dropdown, and Add New Button -->
    <div class="container mx-auto px-4">
        <div class="d-flex justify-content-between mb-6 filter-actions-row">
            <div class="search-box me-3" style="flex-grow: 1;">
                <input type="text" id="searchBox" class="form-control" placeholder="Search by Name or OTP...">
            </div>
            <div class="role-box me-3">
                <select id="roleBox" class="form-select">
                    <?php echo $course_options; // Populate filtered courses ?>
                </select>
            </div>
            <div class="dropdown" style="display:inline-block;">
                <button class="btn btn-primary dropdown-toggle d-flex align-items-center gap-2 shadow actions-btn" type="button" id="actionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-gear"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end shadow rounded" aria-labelledby="actionsDropdown">
                    <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="marker_configuration_list.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                            <i class="bi bi-list-task"></i> Marker Config
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="scorecard_configuration.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                            <i class="bi bi-plus-circle"></i> Add New +
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="export_scorecardConfig_excel.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>">
                            <i class="bi bi-file-earmark-excel"></i> Export Excel
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Scorecard Configuration Table -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th class="sortable" data-type="number">No.</th>
                        <th class="sortable" data-type="string">Golf Course</th>
                        <th class="sortable" data-type="string">Name</th>
                        <th class="sortable" data-type="string">OTP</th>
                        <th class="sortable" data-type="string">Holes</th>
                        <th class="sortable" data-type="string">Status</th>
                        <th>Lock</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="scorecardConfigListTable">
                <?php
                if (!empty($scorecard_data)) {
                    $index = 1;
                    foreach ($scorecard_data as $row) {
                        // Determine account status
                        $status = $row['status'] == 0 ? 'Active' : 'Disabled';
                        $status_class = $row['status'] == 0 ? 'status-active' : 'status-disabled';
                        $course = htmlspecialchars($row['course_name']);
                        $name = htmlspecialchars($row['name']);
                        $otp = trim(htmlspecialchars($row['otp'])); // Trim whitespace

                        // Gather selected holes
                        $selected_holes = [];
                        for ($i = 1; $i <= 18; $i++) {
                            if (isset($row["hole_$i"]) && $row["hole_$i"] == 1) {
                                $selected_holes[] = $i;
                            }
                        }
                        $holes_display = !empty($selected_holes) ? implode(", ", $selected_holes) : 'None';
                        ?>
                        <tr data-course_id="<?php echo htmlspecialchars($row['course_id']); ?>">
                            <td><?php echo $index; ?></td>
                            <td><?php echo $course; ?></td>
                            <td><?php echo $name; ?></td>
                            <td>
                                <button 
                                    class="otp-copy-btn" 
                                    id="otp-<?php echo $row['scorecard_id']; ?>" 
                                    onclick="copyOTP('<?php echo $row['scorecard_id']; ?>')" 
                                    title="Click to copy">
                                    <?php echo $otp; ?>
                                </button>
                                <span id="copy-feedback-<?php echo $row['scorecard_id']; ?>" style="margin-left: 10px; font-size: 12px; color: green; display: none;">
                                    Copied!
                                </span>
                            </td>
                            <td><?php echo $holes_display; ?></td>
                            <td class="<?php echo $status_class; ?>" id="status-<?php echo $row['scorecard_id']; ?>">
                                <?php echo htmlspecialchars($status); ?>
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" <?php echo ($row['status'] == 1 ? 'checked' : ''); ?> onchange="toggleStatus(this, '<?php echo $row['scorecard_id']; ?>')">
                                </div>
                            </td>
                            <td>
                                <a href="edit_scorecardConfig.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>&scorecard_id=<?php echo urlencode($row['scorecard_id']); ?>" class="icon edit-icon" title="Edit Scorecard">
                                    <i class="bi bi-pencil"></i>
                                </a>

                                <!-- Optional: Add Delete functionality -->
                                <!--
                                <a href="delete_scorecard.php?id=<?php echo $row['scorecard_id']; ?>&event_mgm_id=<?php echo urlencode($event_mgm_id); ?>" class="icon delete-icon" onclick="return confirm('Are you sure you want to delete this scorecard?');" title="Delete Scorecard">
                                    <i class="bi bi-trash"></i>
                                </a>
                                -->
                            </td>
                        </tr>
                        <?php
                        $index++;
                    }
                } else {
                    echo "<tr><td colspan='8'>No scorecard configurations found for this Event Management ID.</td></tr>";
                }
                ?>
                </tbody>
            </table>
        </div>

    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
function copyOTP(scorecardId) {
    const otpElement = document.getElementById(`otp-${scorecardId}`);
    const feedbackElement = document.getElementById(`copy-feedback-${scorecardId}`);
    const otpText = otpElement.textContent.trim(); // Trim whitespace

    // Copy OTP to the clipboard
    navigator.clipboard.writeText(otpText).then(() => {
        // Show the "Copied!" message
        feedbackElement.style.display = 'inline';

        // Hide the message after 2 seconds
        setTimeout(() => {
            feedbackElement.style.display = 'none';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy OTP: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = otpText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        // Show feedback even with fallback
        feedbackElement.style.display = 'inline';
        setTimeout(() => {
            feedbackElement.style.display = 'none';
        }, 2000);
    });
}


function toggleStatus(switchElement, scorecard_id) {
    // When the switch is checked, it means the scorecard should be Disabled (status = 1)
    const disabled = switchElement.checked;
    const status = disabled ? 1 : 0;  // If switch is on, status is 1 (Disabled), otherwise it's 0 (Active)
    const statusCell = document.getElementById('status-' + scorecard_id);
    const newStatus = disabled ? 'Disabled' : 'Active';
    const newStatusClass = disabled ? 'status-disabled' : 'status-active';

    // Update the status visually
    statusCell.textContent = newStatus;
    statusCell.className = newStatusClass;

    // Disable the checkbox to prevent multiple clicks
    switchElement.disabled = true;

    // Send AJAX request to update status in the database
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '', true); // Send request to the same file
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onload = function() {
        // Re-enable the checkbox after the request completes
        switchElement.disabled = false;

        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    // Display a success message without alert (optional)
                    alert('Scorecard status updated successfully!');
                } else {
                    alert('Failed to update status: ' + response.error);
                    // Revert changes if update fails
                    switchElement.checked = !disabled;
                    statusCell.textContent = !disabled ? 'Disabled' : 'Active';
                    statusCell.className = !disabled ? 'status-disabled' : 'status-active';
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                alert('An unexpected error occurred.');
                // Revert changes in case of JSON parse error
                switchElement.checked = !disabled ;
                statusCell.textContent = !disabled  ? 'Disabled' : 'Active';
                statusCell.className = !disabled  ? 'status-disabled' : 'status-active';
            }
        } else {
            console.error('AJAX request failed with status:', xhr.status);
            alert('Failed to update status. Please try again.');
            // Revert changes on server error
            switchElement.checked = !disabled ;
            statusCell.textContent = !disabled  ? 'Disabled' : 'Active';
            statusCell.className = !disabled  ? 'status-disabled' : 'status-active';
        }
    };

    // Log the data being sent for debugging (optional, remove in production)
    console.log('scorecard_id=' + encodeURIComponent(scorecard_id) + '&status=' + status);

    // Send the request
    xhr.send('scorecard_id=' + encodeURIComponent(scorecard_id) + '&status=' + status);
}


    </script>

    <script>

        // Function to toggle dropdown visibility
        function toggleDropdown(event) {
            event.preventDefault(); // Prevent the default action
            event.stopPropagation(); // Prevent the event from bubbling up

            const dropdownMenu = event.currentTarget.nextElementSibling;
            const icon = event.currentTarget.querySelector('i.fas');

            // Close all other open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                    const otherIcon = menu.previousElementSibling.querySelector('i.fas');
                    if (otherIcon) {
                        otherIcon.classList.remove('fa-chevron-up');
                        otherIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Toggle the current dropdown
            const isShown = dropdownMenu.classList.contains('show');
            dropdownMenu.classList.toggle('show', !isShown);
            icon.classList.toggle('fa-chevron-down', isShown);
            icon.classList.toggle('fa-chevron-up', !isShown);
        }

        // Function to close dropdown when clicking outside
        function closeDropdown(event) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    menu.classList.remove('show');
                    const icon = menu.previousElementSibling.querySelector('i.fas');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                }
            });
        }

        // Event listener to close dropdown on clicking outside
        document.addEventListener('click', closeDropdown);

        // Event listeners for each dropdown toggle button
        document.querySelectorAll('.dropdown > a').forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            const userListTable = document.getElementById('scorecardConfigListTable'); // Updated selector
            const roleBox = document.getElementById('roleBox');
            const searchBox = document.getElementById('searchBox');
            let sortDirection = {};

            // Filter by Course
            roleBox.addEventListener('change', filterUsers);

            // Search by Name or OTP
            searchBox.addEventListener('input', filterUsers);

            // Sorting functionality
            document.querySelectorAll('.table th.sortable').forEach(header => {
                header.addEventListener('click', () => sortTable(header));
            });

            function filterUsers() {
                const selectedCourseId = roleBox.value.toLowerCase();
                const searchTerm = searchBox.value.toLowerCase();
                const rows = userListTable.querySelectorAll('tr');

                rows.forEach(row => {
                    const courseId = row.getAttribute('data-course_id').toLowerCase();
                    const nameText = row.querySelectorAll('td')[2].textContent.toLowerCase(); // Name in the third column
                    const otpText = row.querySelectorAll('td')[3].textContent.toLowerCase();  // OTP in the fourth column

                    const courseMatch = selectedCourseId === 'all' || courseId === selectedCourseId;
                    const searchMatch = nameText.includes(searchTerm) || otpText.includes(searchTerm);

                    row.style.display = courseMatch && searchMatch ? '' : 'none';
                });
            }

            function sortTable(header) {
                const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                const dataType = header.getAttribute('data-type');
                const direction = sortDirection[columnIndex] = !sortDirection[columnIndex];
                const isNumeric = dataType === 'number';

                const rows = Array.from(userListTable.querySelectorAll('tr')).slice(0); // Include all rows including header

                // Exclude the header row from sorting
                const headerRow = rows.shift();

                rows.sort((a, b) => {
                    const aText = a.children[columnIndex].textContent.trim();
                    const bText = b.children[columnIndex].textContent.trim();

                    if (isNumeric) {
                        return direction ? aText - bText : bText - aText;
                    } else {
                        return direction ? aText.localeCompare(bText) : bText.localeCompare(aText);
                    }
                });

                // Re-append sorted rows
                userListTable.innerHTML = '';
                userListTable.appendChild(headerRow); // Re-add header row
                rows.forEach(row => userListTable.appendChild(row));

                // Update sort indicators
                document.querySelectorAll('.table th.sortable').forEach(th => {
                    th.classList.remove('sorted-asc', 'sorted-desc');
                });
                header.classList.add(direction ? 'sorted-asc' : 'sorted-desc');
            }


            // Delete User with confirmation alert
            window.deleteUser = function(icon) {
                if (confirm("Are you sure you want to delete this user?")) {
                    const row = icon.closest('tr');
                    row.remove();
                }
            };
        });
    </script>
</body>
</html>
