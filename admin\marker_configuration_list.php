<?php 
// Set timezone to UTC+08
date_default_timezone_set('Asia/Singapore');

session_start();
require("../database.php");

// Proceed with fetching and displaying the page content only for GET requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Fetch the event_mgm_id from the URL
    $event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
    if (!$event_mgm_id) {
        die('Event Management ID not provided.');
    }

    $adminisLoggedIn = isset($_SESSION['profile_email']);
    if ($adminisLoggedIn) {
        $admin_name = $_SESSION['profile_name'];
        $admin_roleid = $_SESSION['profile_role_id'];
        $session_profile_id = $_SESSION['profile_id'];

        // Check if $session_profile_id exists in event_role_mgm
        $query = "SELECT COUNT(*) as total_records, 
                         SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
                  FROM event_role_mgm 
                  WHERE profile_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
        $stmt->execute();
        $stmt->bind_result($total_records, $match_event);
        $stmt->fetch();
        $stmt->close();

        if ($total_records > 0) {
            if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
                header("Location: ../index.php?error=accessdenied");
                exit();
            }
        } else {
            if ($admin_roleid != 'gPHOfKV0sL') {
                header("Location: ../index.php?error=accessdenied");
                exit();
            }
        }
    } else {
        header("Location: ../login.php?error=pagenotfound");
        exit();
    }

    // Fetch event name based on event_mgm_id
    $event_name = '';
    if ($event_mgm_id !== '') {
        $stmt = $conn->prepare("SELECT event_name FROM event_mgm WHERE event_mgm_id = ?");
        $stmt->bind_param("s", $event_mgm_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $event = $result->fetch_assoc();
        $event_name = $event ? $event['event_name'] : 'Unknown Event';
        $stmt->close();
    }

    // Fetch unique flight list names for filtering
    $flightListQuery = "
        SELECT DISTINCT fl.flight_list_name 
        FROM marker_assignments ma
        JOIN flight_list fl ON ma.flight_list_name = fl.flight_list_name
        WHERE fl.event_id IN (SELECT event_id FROM event_mgm WHERE event_mgm_id = ?)
        ORDER BY fl.flight_list_name";
    $stmt = $conn->prepare($flightListQuery);
    $stmt->bind_param("s", $event_mgm_id);
    $stmt->execute();
    $flightListResult = $stmt->get_result();
    $flightLists = [];
    while ($row = $flightListResult->fetch_assoc()) {
        $flightLists[] = $row['flight_list_name'];
    }
    $stmt->close();

    // Fetch marker assignments - modified to show unique combinations
    $markerQuery = "
        SELECT DISTINCT
            ma.flight_list_name,
            ma.round_number,
            ma.marking_format,
            MAX(ma.status) as status,
            MIN(ma.unlock_datetime) as unlock_datetime
        FROM marker_assignments ma
        WHERE ma.event_id IN (SELECT event_id FROM event_mgm WHERE event_mgm_id = ?)
        GROUP BY ma.flight_list_name, ma.round_number, ma.marking_format
        ORDER BY ma.flight_list_name, ma.round_number";
    
    $stmt = $conn->prepare($markerQuery);
    $stmt->bind_param("s", $event_mgm_id);
    $stmt->execute();
    $markerResult = $stmt->get_result();
    $markerData = [];
    while ($row = $markerResult->fetch_assoc()) {
        $markerData[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marker Configuration List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Styling */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Main Title Styling */
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 24px 0;
            text-align: center;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        .event-name {
            font-size: 1.125rem;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: center;
            color: #6b7280;
        }

        /* 3. Professional Header Section */
        .header-section {
            background-color: #ffffff;
            padding: 32px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 4. Professional Card Layout */
        .control-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        .table-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        /* 5. Professional Table Styling */
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: white;
            margin: 0;
        }

        .table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            vertical-align: middle;
            text-align: center;
        }

        /* Flight List column left alignment */
        .table th:nth-child(2),
        .table td:nth-child(2) {
            text-align: left;
        }

        /* Ensure Status column stays centered */
        .table th:nth-child(6),
        .table td:nth-child(6) {
            text-align: center !important;
        }

        /* Auto-Unlock Schedule column styling */
        .table th:nth-child(5),
        .table td:nth-child(5) {
            min-width: 180px;
            white-space: nowrap;
        }

        /* Ensure table shows all columns */
        .table {
            table-layout: auto;
            min-width: 100%;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 6. Professional Form Controls */
        .form-control, .form-select {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-control:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .flight-list-box {
            min-width: 200px;
        }

        /* 7. Professional Button Styling */
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
            color: white;
        }

        /* 8. Action Icons */
        .edit-icon {
            color: #3b82f6;
            font-size: 1.125rem;
            padding: 6px;
            border-radius: 6px;
            transition: all 0.2s;
            text-decoration: none;
        }

        .edit-icon:hover {
            background: #eff6ff;
            color: #2563eb;
        }

        /* 9. Professional Status Elements */
        .form-switch {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .form-switch .form-check-input {
            width: 2.5rem;
            height: 1.25rem;
            margin: 0;
        }

        .form-check-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
            white-space: nowrap;
        }

        /* Status label colors */
        .status-active {
            color: #059669 !important;
        }

        .status-locked {
            color: #dc2626 !important;
        }

        /* 10. Status Indicators */
        .text-green-600 {
            color: #059669 !important;
            font-weight: 500;
        }

        .text-orange-600 {
            color: #d97706 !important;
            font-weight: 500;
        }

        .text-gray-400 {
            color: #9ca3af !important;
        }

        /* 11. Professional Responsive Design */
        .filter-actions-row {
            gap: 20px;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 1.5rem;
                margin: 16px 0;
            }

            .event-name {
                font-size: 1rem;
                margin-bottom: 12px;
            }

            .header-section {
                padding: 20px 0;
                margin-bottom: 20px;
            }

            .control-card {
                padding: 16px;
                margin-bottom: 16px;
            }

            .filter-actions-row {
                flex-direction: column !important;
                gap: 16px !important;
            }

            .search-box, .flight-list-box {
                width: 100% !important;
                min-width: 100% !important;
            }

            .form-control, .form-select {
                padding: 10px 14px;
                font-size: 0.875rem;
            }

            .d-flex.gap-2 {
                flex-direction: column !important;
                gap: 12px !important;
                width: 100%;
            }

            .btn {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
            }

            .table-container {
                overflow-x: auto;
                border-radius: 8px;
            }

            .table {
                min-width: 800px;
            }

            .table th, .table td {
                padding: 12px 8px;
                font-size: 0.8rem;
                white-space: nowrap;
            }

            /* Maintain Flight List left alignment on mobile */
            .table th:nth-child(2),
            .table td:nth-child(2) {
                text-align: left !important;
            }

            /* Ensure Status column stays centered on mobile */
            .table th:nth-child(6),
            .table td:nth-child(6) {
                text-align: center !important;
            }

            .table th {
                font-size: 0.7rem;
            }

            .form-switch .form-check-input {
                width: 2rem;
                height: 1rem;
            }

            .form-check-label {
                font-size: 0.7rem;
                font-weight: 600;
            }
        }

        /* 12. Extra Small Mobile Devices */
        @media (max-width: 576px) {
            .container {
                padding: 0 8px;
            }

            .main-title {
                font-size: 1.25rem;
                margin: 12px 0;
            }

            .event-name {
                font-size: 0.875rem;
            }

            .header-section {
                padding: 16px 0;
                margin-bottom: 16px;
            }

            .control-card {
                padding: 12px;
                margin-bottom: 12px;
            }

            .form-control, .form-select {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.875rem;
            }

            .table th, .table td {
                padding: 8px 6px;
                font-size: 0.75rem;
            }

            .table th {
                font-size: 0.65rem;
            }

            /* Maintain Flight List left alignment on extra small mobile */
            .table th:nth-child(2),
            .table td:nth-child(2) {
                text-align: left !important;
            }

            /* Ensure Status column stays centered on extra small mobile */
            .table th:nth-child(6),
            .table td:nth-child(6) {
                text-align: center !important;
            }

            .form-switch .form-check-input {
                width: 1.75rem;
                height: 0.875rem;
            }

            .form-check-label {
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Marker Configuration List</h2>
        <p class="event-name"><?= htmlspecialchars($event_name) ?></p>
    </div>

    <!-- Professional Control Section -->
    <div class="container mx-auto px-4">
        <div class="control-card">
            <div class="d-flex justify-content-between align-items-center filter-actions-row">
                <div class="search-box">
                    <input type="text" id="searchBox" class="form-control" placeholder="Search by Flight List, Round, or Format...">
                </div>
                <div class="flight-list-box">
                    <select id="flightListBox" class="form-select">
                        <option value="all" selected>All Flight Lists</option>
                        <?php foreach ($flightLists as $flightList): ?>
                            <option value="<?= htmlspecialchars($flightList) ?>">
                                <?= htmlspecialchars($flightList) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="d-flex gap-2">
                    <a href="marker_configuration.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>" class="btn btn-primary">Add New</a>
                    <a href="scorecardConfig_list.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>" class="btn btn-secondary">Back to Scorecard</a>
                </div>
            </div>
        </div>

        <!-- Marker Configuration Table -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th class="sortable" data-type="number">No.</th>
                        <th class="sortable" data-type="string">Flight List</th>
                        <th class="sortable" data-type="number">Round</th>
                        <th class="sortable" data-type="string">Format</th>
                        <th>Auto-Unlock Schedule</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="markerConfigListTable">
                <?php
                if (!empty($markerData)) {
                    $index = 1;
                    foreach ($markerData as $row) {
                        ?>
                        <tr data-flight-list="<?php echo htmlspecialchars($row['flight_list_name']); ?>">
                            <td><?php echo $index; ?></td>
                            <td><?php echo htmlspecialchars($row['flight_list_name']); ?></td>
                            <td><?php echo htmlspecialchars($row['round_number']); ?></td>
                            <td><?php echo htmlspecialchars($row['marking_format']); ?></td>
                            <td>
                                <?php 
                                if ($row['unlock_datetime']) {
                                    $unlock_dt = new DateTime($row['unlock_datetime']);
                                    $current_time = new DateTime();
                                    
                                    echo '<span class="text-sm">' . $unlock_dt->format('d/m/Y - g:i A') . '</span>';
                                    
                                    // Show status indicator
                                    if ($current_time >= $unlock_dt) {
                                        echo '<br><span class="text-xs text-green-600">✓ Auto-unlocked</span>';
                                    } else {
                                        echo '<br><span class="text-xs text-orange-600">⏰ Pending unlock</span>';
                                    }
                                } else {
                                    echo '<span class="text-gray-400">Not set</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input ban-switch" type="checkbox" role="switch"
                                        data-flight-list="<?php echo htmlspecialchars($row['flight_list_name']); ?>"
                                        data-round="<?php echo htmlspecialchars($row['round_number']); ?>"
                                        <?php echo ($row['status'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label <?php echo ($row['status'] == 1) ? 'status-locked' : 'status-active'; ?>">
                                        <?php echo ($row['status'] == 1) ? 'Locked' : 'Active'; ?>
                                    </label>
                                </div>
                            </td>
                            <td>
                                <a href="edit_marker_config.php?event_mgm_id=<?php echo urlencode($event_mgm_id); ?>&flight_list_name=<?php echo urlencode($row['flight_list_name']); ?>&round_number=<?php echo urlencode($row['round_number']); ?>" class="icon edit-icon" title="Edit Marker Configuration">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </td>
                        </tr>
                        <?php
                        $index++;
                    }
                } else {
                    echo "<tr><td colspan='7'>No marker configurations found for this Event Management ID.</td></tr>";
                }
                ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const markerListTable = document.getElementById('markerConfigListTable');
        const flightListBox = document.getElementById('flightListBox');
        const searchBox = document.getElementById('searchBox');
        let sortDirection = {};

        // Auto-update marker status based on datetime settings
        function autoUpdateMarkerStatus(showAlert = false) {
            fetch('auto_update_marker_status.php')
                .then(response => response.json())
                .then(data => {
                    console.log('Auto-update response:', data);
                    if (data.success) {
                        if (data.total_updates > 0) {
                            console.log(`Marker status auto-updated: ${data.total_updates} records updated`);
                            if (showAlert) {
                                alert(`✅ ${data.total_updates} records were unlocked!`);
                            }
                            // Reload the page to show updated status
                            location.reload();
                        } else {
                            console.log('No records needed updating');
                            if (showAlert) {
                                alert('✅ Auto-update check completed. No records needed updating at this time.');
                            }
                        }
                    } else {
                        console.error('Auto-update failed:', data.error);
                        if (showAlert) {
                            alert('❌ Error: ' + data.error);
                        }
                    }
                })
                .catch(error => {
                    console.error('Auto-update error:', error);
                    if (showAlert) {
                        alert('❌ Network error occurred while checking for updates.');
                    }
                });
        }



        // Run auto-update immediately and then every 30 seconds
        autoUpdateMarkerStatus();
        setInterval(autoUpdateMarkerStatus, 30000);

        // Filter by Flight List
        flightListBox.addEventListener('change', filterMarkers);

        // Search by Flight List, Round, or Format
        searchBox.addEventListener('input', filterMarkers);

        // Ban switch toggle functionality
        document.querySelectorAll('.ban-switch').forEach(switchEl => {
            switchEl.addEventListener('change', function() {
                const flightList = this.getAttribute('data-flight-list');
                const round = this.getAttribute('data-round');
                const status = this.checked ? 1 : 0;
                
                // Show loading indicator
                this.disabled = true;
                
                // Send AJAX request to update status
                fetch('update_marker_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `flight_list_name=${encodeURIComponent(flightList)}&round_number=${encodeURIComponent(round)}&status=${status}`
                })
                .then(response => response.json())
                .then(data => {
                    // Re-enable the switch
                    this.disabled = false;
                    
                    if (data.success) {
                        // Success message using alert
                        alert(data.message);
                        // Update the label
                        const label = this.parentNode.querySelector('.form-check-label');
                        if (label) {
                            label.textContent = this.checked ? 'Locked' : 'Active';
                        }
                    } else {
                        // Error handling
                        alert('Error: ' + data.error);
                        // Revert switch to original state
                        this.checked = !this.checked;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status.');
                    this.disabled = false;
                    // Revert switch to original state
                    this.checked = !this.checked;
                });
            });
        });

        // Sorting functionality
        document.querySelectorAll('.table th.sortable').forEach(header => {
            header.addEventListener('click', () => sortTable(header));
        });

        function filterMarkers() {
            const selectedFlightList = flightListBox.value.toLowerCase();
            const searchTerm = searchBox.value.toLowerCase();
            const rows = markerListTable.querySelectorAll('tr');

            rows.forEach(row => {
                const flightList = row.getAttribute('data-flight-list')?.toLowerCase();
                const flightListText = row.querySelectorAll('td')[1]?.textContent.toLowerCase();
                const roundText = row.querySelectorAll('td')[2]?.textContent.toLowerCase();
                const formatText = row.querySelectorAll('td')[3]?.textContent.toLowerCase();

                const flightListMatch = selectedFlightList === 'all' || flightList === selectedFlightList;
                const searchMatch = flightListText?.includes(searchTerm) || 
                                  roundText?.includes(searchTerm) ||
                                  formatText?.includes(searchTerm);

                row.style.display = flightListMatch && searchMatch ? '' : 'none';
            });
        }

        function sortTable(header) {
            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
            const dataType = header.getAttribute('data-type');
            const direction = sortDirection[columnIndex] = !sortDirection[columnIndex];
            const isNumeric = dataType === 'number';

            const rows = Array.from(markerListTable.querySelectorAll('tr'));
            const headerRow = rows.shift();

            rows.sort((a, b) => {
                const aText = a.children[columnIndex].textContent.trim();
                const bText = b.children[columnIndex].textContent.trim();

                if (isNumeric) {
                    return direction ? aText - bText : bText - aText;
                } else {
                    return direction ? aText.localeCompare(bText) : bText.localeCompare(aText);
                }
            });

            markerListTable.innerHTML = '';
            markerListTable.appendChild(headerRow);
            rows.forEach(row => markerListTable.appendChild(row));

            document.querySelectorAll('.table th.sortable').forEach(th => {
                th.classList.remove('sorted-asc', 'sorted-desc');
            });
            header.classList.add(direction ? 'sorted-asc' : 'sorted-desc');
        }
    });
    </script>
</body>
</html> 