<?php 
session_start();
require("database.php");

// Check if the user is logged in
if (isset($_SESSION['profile_email'])) {
    $name = $_SESSION['profile_name'];
    $roleid = $_SESSION['profile_role_id'];
    $profile_id = $_SESSION['profile_id'];
    $profile_email = $_SESSION['profile_email'];
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Determine if the user can see the "Scorecard" button
$canSeeScorecardButton = false;
$isAdmin = false;
$isCaptain = false;
$captain_flight_list_name = '';
$captain_round_number = '';

// Fetch the event_mgm_id from the URL
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
if (!$event_mgm_id) {
    die('Event Management ID not provided.');
}

// First Condition: Check if $roleid is 'gPHOfKV0sL' (Admin)
if ($roleid == 'gPHOfKV0sL') {
    $canSeeScorecardButton = true;
    $isAdmin = true;
} else {
    // Second Condition: Check if captain_id is 1 and profile_id matches (Captain)
    $stmt_profile = $conn->prepare("
        SELECT fl.flight_list_name, fl.round_number, r.nationality 
        FROM flight_list fl
        JOIN registration_form r ON fl.profile_id = r.profile_id
        WHERE fl.profile_id = ? AND fl.captain_id = 1
        LIMIT 1
    ");
    $stmt_profile->bind_param("s", $profile_id);
    $stmt_profile->execute();
    $stmt_profile->bind_result($captain_flight_list_name, $captain_round_number, $user_nationality);
    if ($stmt_profile->fetch()) {
        $canSeeScorecardButton = true;
        $isCaptain = true;
    }
    $stmt_profile->close();

    // Third Condition: Check if the user has a scorecard configuration for this event
    if (!$canSeeScorecardButton) {
        $stmt_scorecard = $conn->prepare("
            SELECT course_id FROM scorecard_configuration 
            WHERE event_mgm_id = ? AND name = ?
            LIMIT 1
        ");
        $stmt_scorecard->bind_param("ss", $event_mgm_id, $name); // Changed to $name
        $stmt_scorecard->execute(); // Execute the statement
        $stmt_scorecard->bind_result($user_course_id);
        if ($stmt_scorecard->fetch()) {
            $canSeeScorecardButton = true;
        }
        $stmt_scorecard->close();
    }
}

// Fetch all event_id associated with the given event_mgm_id
$event_query = "
    SELECT em.event_id, em.event_num_participant
    FROM event_mgm AS em
    WHERE em.event_mgm_id = ?";
$stmt_event = $conn->prepare($event_query);
$stmt_event->bind_param("s", $event_mgm_id);
$stmt_event->execute();
$event_result = $stmt_event->get_result();

if ($event_result->num_rows == 0) {
    die('Invalid event_mgm_id or no event found.');
}

$event_ids = [];
$event_num_participant = null;
while ($event = $event_result->fetch_assoc()) {
    $event_ids[] = $event['event_id'];
    // Assuming event_num_participant is the same for all event_id under the same event_mgm_id
    if (is_null($event_num_participant)) {
        $event_num_participant = $event['event_num_participant'];
    }
}

$stmt_event->close();

// Prepare placeholders and types for IN clause
$in_placeholders = implode(',', array_fill(0, count($event_ids), '?'));
$types = str_repeat('s', count($event_ids));

// Fetch course_id(s) from custom_hole table
$course_query = "
    SELECT DISTINCT course_id
    FROM custom_hole
    WHERE event_id IN ($in_placeholders)";
$stmt_course = $conn->prepare($course_query);
$stmt_course->bind_param($types, ...$event_ids);
$stmt_course->execute();
$course_result = $stmt_course->get_result();

$course_ids = [];
if ($course_result->num_rows > 0) {
    while ($course = $course_result->fetch_assoc()) {
        $course_ids[] = $course['course_id'];
    }
}

$stmt_course->close();

// Fetch all course_ids and their names into an associative array
$course_id_name_map = [];
if (count($course_ids) > 0) {
    $course_in_placeholders = implode(',', array_fill(0, count($course_ids), '?'));
    $course_types = str_repeat('s', count($course_ids));

    $course_name_query = "
        SELECT course_id, course_name
        FROM course_info
        WHERE course_id IN ($course_in_placeholders)";
    $stmt_course_name = $conn->prepare($course_name_query);
    $stmt_course_name->bind_param($course_types, ...$course_ids);
    $stmt_course_name->execute();
    $course_name_result = $stmt_course_name->get_result();

    while ($course_info = $course_name_result->fetch_assoc()) {
        $course_id_name_map[$course_info['course_id']] = $course_info['course_name'];
    }
    $stmt_course_name->close();
}

// Fetch distinct flight_list_names and their latest round_numbers
$flight_list_name_query = "
    SELECT fl.flight_list_name, MAX(fl.round_number) as latest_round_number
    FROM flight_list fl
    WHERE fl.event_id IN ($in_placeholders)
    GROUP BY fl.flight_list_name
    ORDER BY fl.flight_list_name ASC";
$stmt_flight_list_name = $conn->prepare($flight_list_name_query);
$stmt_flight_list_name->bind_param($types, ...$event_ids);
$stmt_flight_list_name->execute();
$flight_list_name_result = $stmt_flight_list_name->get_result();

$flight_list_names = [];
$flight_list_latest_rounds = []; // To store latest round numbers for each flight_list_name
if ($flight_list_name_result) {
    while ($row = $flight_list_name_result->fetch_assoc()) {
        $flight_list_name = $row['flight_list_name'];
        $latest_round_number = $row['latest_round_number'];
        $flight_list_names[] = $flight_list_name;
        $flight_list_latest_rounds[$flight_list_name] = $latest_round_number;
    }
}

$stmt_flight_list_name->close();

// Fetch user's course_id(s) from scorecard_configuration
$user_course_ids = [];
if (!$isAdmin) {
    $stmt_user_course = $conn->prepare("
        SELECT course_id FROM scorecard_configuration
        WHERE event_mgm_id = ? AND name = ?");
    $stmt_user_course->bind_param("ss", $event_mgm_id, $name);
    $stmt_user_course->execute();
    $result_user_course = $stmt_user_course->get_result();
    while ($row = $result_user_course->fetch_assoc()) {
        $user_course_ids[] = $row['course_id'];
    }
    $stmt_user_course->close();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Draw List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Font Size Adjustment */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Main Title Styling */
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 24px 0;
            text-align: center;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        /* 3. Professional Card Layout */
        .control-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        /* 4. Header Controls Styling */
        .header-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }

        .header-controls-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
            width: 100%;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter {
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
            min-width: 200px;
        }

        .filter:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 5. Professional Button Styling */
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-print {
            background: #059669;
            color: white;
        }

        .btn-print:hover {
            background: #047857;
        }

        /* Touch feedback for mobile */
        .touch-active {
            transform: scale(0.95) !important;
            transition: transform 0.1s ease !important;
        }

        /* Mobile table scrolling optimization */
        .table-responsive.scrolling {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* Mobile-friendly alert */
        .mobile-alert {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            z-index: 9999;
            max-width: 90%;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* 6. Flight Detail Box Styling */
        .flight-detail {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 20px;
            padding: 20px;
            font-size: 0.875rem;
        }

        .flight-detail h5 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }

        .flight-detail h6 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #4b5563;
            margin-top: 16px;
            margin-bottom: 16px;
            text-align: center;
        }

        /* 8. No Records Message Styling */
        .no-records-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            background-color: #f9fafb;
            border-radius: 12px;
            border: 2px dashed #d1d5db;
            font-size: 0.875rem;
        }

        .no-records-message {
            color: #6b7280;
            font-weight: 500;
        }

        /* 7. Professional Table Styling */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            margin-bottom: 16px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: white;
        }

        table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            vertical-align: middle;
        }

        table tbody tr:hover {
            background-color: #f9fafb;
        }

        table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 8. Flag Styling */
        .flag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #f1f1f1;
            border-radius: 4px;
            font-size: 0.75rem;
            color: #333;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .flag-icon {
            display: inline-block;
            width: 30px !important;
            height: 22px !important;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            border-radius: 2px;
            margin: 0 auto;
            cursor: help;
        }

        /* Column headers */
        table th:first-child {
            width: 60px;
            text-align: center;
        }

        table td:first-child {
            text-align: center;
        }

        .player-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background-color: #f1f1f1;
            border-radius: 50%;
            font-weight: 600;
            font-size: 0.75rem;
            color: #333;
            margin: 0 auto;
        }

        /* 9. Hover Effects for Table Rows */
        table tbody tr {
            border-bottom: 1px solid #f1f1f1;
            transition: all 0.2s ease;
        }

        table tbody tr:last-child {
            border-bottom: none;
        }

        table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 10. Professional Section Headers */
        .section-header {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin: 32px 0 24px 0;
            text-align: center;
        }

        .start-header {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .flight-date {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .course-name {
            font-size: 1rem;
            color: #4b5563;
            font-weight: 500;
        }

        /* 11. Search Box Styling */
        .search-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 20px;
            margin-bottom: 24px;
        }

        #playerSearchBox {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        #playerSearchBox:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        #playerSearchBox::placeholder {
            color: #9ca3af;
        }



        /* 12. Loader Styling */
        .loader {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100px;
            font-size: 0.875rem;
            color: #6c757d;
        }

        /* Mobile loader adjustments */
        @media (max-width: 576px) {
            .loader {
                height: 80px;
                font-size: 0.8rem;
            }
        }

        .loader:after {
            content: " ";
            display: block;
            width: 20px;
            height: 20px;
            margin-left: 10px;
            border-radius: 50%;
            border: 2px solid #6c757d;
            border-color: #6c757d transparent #6c757d transparent;
            animation: loader 1.2s linear infinite;
        }

        @keyframes loader {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* Show header center on print */
        .header-section-print {
            display: none;
        }

        /* 12. Responsive Design */
        @media (max-width: 768px) {
            .header-controls {
                flex-direction: column;
                gap: 16px;
            }

            .header-controls-row {
                flex-direction: column;
                gap: 12px;
                justify-content: center;
            }

            .form-group {
                width: 100%;
            }

            .filter {
                min-width: 100%;
            }

            .flight-detail {
                padding: 16px;
            }

            .section-header {
                padding: 16px;
            }

            .main-title {
                font-size: 1.5rem;
            }

            .search-card {
                padding: 16px;
            }

            table th,
            table td {
                padding: 8px;
                font-size: 0.8rem;
            }
        }

        /* Mobile-specific optimizations */
        @media (max-width: 576px) {
            .container {
                padding: 0 10px;
            }

            .main-title {
                font-size: 1.3rem;
                margin: 15px 0;
            }

            .header-controls {
                margin-bottom: 15px;
            }

            .header-controls-row {
                gap: 10px;
            }

            .filter, .btn-primary, .btn-secondary, .btn-print {
                max-width: 100%;
                font-size: 0.9rem;
                padding: 10px 15px;
            }

            .flight-detail {
                padding: 12px;
                margin-bottom: 15px;
            }

            .flight-detail h5 {
                font-size: 1.1rem;
                margin-bottom: 8px;
            }

            .flight-detail h6 {
                font-size: 0.9rem;
                margin-bottom: 12px;
            }

            .start-header {
                font-size: 1.2rem;
                margin-top: 20px;
                margin-bottom: 3px;
            }

            .course-name {
                font-size: 0.9rem;
                margin-bottom: 15px;
            }

            .flight-date {
                font-size: 0.9rem;
                margin-bottom: 8px;
            }

            /* Table optimizations for mobile */
            .table-responsive {
                border: none;
            }

            table {
                font-size: 0.75rem;
            }

            table th,
            table td {
                padding: 6px 4px;
                vertical-align: middle;
            }

            /* Reduce column widths for mobile */
            table th:first-child,
            table td:first-child {
                width: 40px;
                min-width: 40px;
            }

            table th:nth-child(2),
            table td:nth-child(2) {
                min-width: 80px;
            }

            table th:nth-child(3),
            table td:nth-child(3) {
                min-width: 60px;
            }

            table th:nth-child(4),
            table td:nth-child(4) {
                min-width: 70px;
            }
            
            /* Flag and player number adjustments */
            .flag-icon {
                width: 24px !important;
                height: 18px !important;
            }
            
            .player-number {
                width: 20px;
                height: 20px;
                line-height: 20px;
                font-size: 0.7rem;
            }

            .unknown-flag {
                font-size: 0.7rem;
                padding: 1px 4px;
            }

            /* Hide less important columns on very small screens */
            @media (max-width: 480px) {
                /* Removed hiding of 3rd column (Score) so it is always visible on mobile */
                /* table th:nth-child(3),
                table td:nth-child(3) {
                    display: none;
                } */

                /* Hide marker column on very small screens if it exists */
                table th:nth-child(5),
                table td:nth-child(5) {
                    display: none;
                }

                .btn-primary, .btn-secondary, .btn-print {
                    font-size: 0.85rem;
                    padding: 8px 12px;
            }
            }

            /* Medium mobile screens - show marker column but with smaller text */
            @media (min-width: 481px) and (max-width: 576px) {
                table th:nth-child(5),
                table td:nth-child(5) {
                    font-size: 0.7rem;
                    padding: 4px 2px;
                }
            }

            /* Landscape orientation adjustments */
            @media (max-width: 768px) and (orientation: landscape) {
                .header-controls-row {
                    flex-direction: row;
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .filter, .btn-primary, .btn-secondary, .btn-print {
                    width: auto;
                    max-width: 200px;
                    margin: 5px;
            }
            }
        }

        /* Print Styles */
        @media print {
            /* Reset body styles for print */
            body {
                background-color: white !important;
                color: black !important;
                font-family: 'Times New Roman', serif !important;
                line-height: 1.4 !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* Page setup */
            @page {
                margin: 1cm;
                size: A4 landscape;
            }

            /* Hide non-printable elements */
            .header-section, 
            header, 
            footer, 
            .header-controls,
            .btn-primary,
            .btn-secondary,
            .btn-print,
            .filter,
            .d-none {
                display: none !important;
            }

            /* Print header */
            .header-section-print {
                display: block !important;
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #000;
                padding-bottom: 15px;
            }

            .header-section-print h2 {
                font-size: 28pt !important;
                font-weight: bold !important;
                margin: 0 0 15px 0 !important;
                text-transform: uppercase !important;
                letter-spacing: 2px !important;
            }

            .header-section-print p {
                font-size: 16pt !important;
                margin: 0 0 5px 0 !important;
                color: #333 !important;
            }

            /* Section headers for landscape */
            .start-header {
                font-size: 22pt !important;
                font-weight: bold !important;
                text-align: center !important;
                margin: 25px 0 15px 0 !important;
                text-transform: uppercase !important;
                letter-spacing: 1px !important;
                border-bottom: 1px solid #000 !important;
                padding-bottom: 10px !important;
            }

            .start-header:after {
                display: none !important;
            }

            .course-name {
                font-size: 18pt !important;
                font-weight: bold !important;
                text-align: center !important;
                margin: 12px 0 18px 0 !important;
                color: #333 !important;
            }

            .flight-date {
                font-size: 16pt !important;
                text-align: center !important;
                margin: 8px 0 18px 0 !important;
                color: #666 !important;
                font-style: italic !important;
            }

            /* Flight detail boxes */
            .flight-detail {
                box-shadow: none !important;
                border: 2px solid #000 !important;
                break-inside: avoid !important;
                page-break-inside: avoid !important;
                margin-bottom: 20px !important;
                padding: 15px !important;
                background-color: white !important;
            }

            .flight-detail h5 {
                font-size: 18pt !important;
                font-weight: bold !important;
                text-align: center !important;
                margin: 0 0 12px 0 !important;
                text-transform: uppercase !important;
                background-color: #f0f0f0 !important;
                padding: 10px !important;
                border: 1px solid #000 !important;
            }

            .flight-detail h6 {
                font-size: 16pt !important;
                font-weight: bold !important;
                text-align: center !important;
                margin: 12px 0 18px 0 !important;
                color: #333 !important;
                border-bottom: 1px solid #ccc !important;
                padding-bottom: 8px !important;
            }

            /* Table styles */
            .table-responsive {
                border: none !important;
                margin: 0 !important;
            }

            table {
                width: 100% !important;
                border-collapse: collapse !important;
                font-size: 12pt !important;
                margin: 0 !important;
            }

            table th {
                background-color: #f0f0f0 !important;
                color: #000 !important;
                font-weight: bold !important;
                text-align: center !important;
                padding: 10px 6px !important;
                border: 1px solid #000 !important;
                text-transform: uppercase !important;
                font-size: 11pt !important;
                letter-spacing: 0.5px !important;
            }

            table td {
                padding: 8px 6px !important;
                border: 1px solid #ccc !important;
                text-align: left !important;
                vertical-align: middle !important;
            }

            table tr:nth-child(even) {
                background-color: #f9f9f9 !important;
            }

            /* Flag and nationality styles */
            .flag-icon {
                box-shadow: none !important;
                border: 1px solid #000 !important;
                width: 20px !important;
                height: 15px !important;
                margin: 0 4px 0 0 !important;
                print-color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
            }

            .player-number {
                background-color: #f0f0f0 !important;
                border: 1px solid #000 !important;
                color: #000 !important;
                print-color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
                width: 20px !important;
                height: 20px !important;
                line-height: 20px !important;
                font-size: 9pt !important;
                font-weight: bold !important;
            }

            .unknown-flag {
                border: 1px solid #000 !important;
                background-color: #f0f0f0 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                font-size: 9pt !important;
                font-weight: bold !important;
                padding: 2px 4px !important;
            }

            /* Column specific styles for landscape */
            table th:first-child,
            table td:first-child {
                width: 60px !important;
                text-align: center !important;
            }

            table th:nth-child(2),
            table td:nth-child(2) {
                width: 200px !important;
                font-weight: bold !important;
            }

            table th:nth-child(3),
            table td:nth-child(3) {
                width: 100px !important;
                text-align: center !important;
            }

            table th:nth-child(4),
            table td:nth-child(4) {
                width: 120px !important;
                text-align: center !important;
            }

            table th:nth-child(5),
            table td:nth-child(5) {
                width: 150px !important;
                text-align: center !important;
            }

            /* Page breaks */
            .flight-detail {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* Footer for each page */
            @page {
                @bottom-center {
                    content: "Page " counter(page) " of " counter(pages);
                    font-size: 10pt;
                    color: #666;
                }
            }

            /* Hide any remaining interactive elements */
            button, input, select, textarea {
                display: none !important;
            }

            /* Ensure text is readable */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }

        .printing .header-section, .printing header, .printing footer, .printing .header-controls {
            display: none !important;
        }

        /* Additional print optimizations */
        @media print {
            /* Ensure proper page breaks */
            .row {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* Optimize column layout for landscape print */
            .col-lg-6 {
                width: 48% !important;
                float: left !important;
                margin-right: 2% !important;
                margin-bottom: 20px !important;
            }

            .col-lg-12 {
                width: 100% !important;
                float: none !important;
                margin-bottom: 20px !important;
            }

            /* Clear float after rows */
            .row:after {
                content: "";
                display: table;
                clear: both;
            }

            /* Add spacing between sections */
            .text-center {
                margin-bottom: 20px !important;
            }

            /* Ensure tables don't break across pages */
            .table-responsive {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* Add borders to make sections more distinct */
            .flight-detail {
                border-left: 3px solid #000 !important;
            }

            /* Optimize text for print readability */
            body {
                font-size: 12pt !important;
            }

            /* Add subtle background to alternate rows for better readability */
            table tr:nth-child(odd) {
                background-color: #ffffff !important;
            }

            table tr:nth-child(even) {
                background-color: #f8f8f8 !important;
            }
        }

        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        @media (max-width: 600px) {
            table, th, td {
                font-size: 0.55rem !important;
                padding: 2px 1px !important;
                white-space: nowrap !important;
            }
            .flight-detail {
                padding: 4px !important;
            }
            th, td {
                min-width: unset !important;
                max-width: 60px !important;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            /* Hide only the Marker column on mobile */
            .marker-col {
                display: none !important;
            }
        }
        /* Remove swipe hint */
        .table-responsive::after { display: none !important; }
        @media (min-width: 601px) {
            .table-responsive::after {
                display: none;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <?php include("header.php"); ?>

    <div class="header-section">
        <h2 class="main-title">Draw List</h2>
    </div>

    <!-- Print-only header -->
    <div class="header-section-print">
        <h2>Draw List</h2>
        <p id="printEventDetails"></p>
        <p id="printDateTime"></p>
    </div>

    <div class="container">
        <div class="control-card">
            <div class="header-controls">
                <div class="header-controls-row">
                    <!-- Flight List Name Dropdown -->
                    <div class="form-group">
                        <label for="flightListName">Flight List</label>
                        <select class="form-control filter" id="flightListName" name="flight_list_name">
                            <option value="" disabled selected>Select Flight List Name</option>
                            <?php foreach ($flight_list_names as $name): ?>
                                <option value="<?php echo htmlspecialchars($name); ?>">
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Round Filter Dropdown -->
                    <div class="form-group">
                        <label for="roundFilter">Round</label>
                        <select class="form-control filter" id="roundFilter" disabled>
                            <option value="" disabled selected>Select Round</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <!-- Search Box -->
                    <div class="form-group">
                        <label for="playerSearchBox">Search Player</label>
                        <input type="text" id="playerSearchBox" class="filter" placeholder="Search player name...">
                    </div>

                    <!-- Scorecard Button (conditionally displayed) -->
                    <!--
                    <?php if ($canSeeScorecardButton): ?>
                        <button class="btn btn-secondary" id="scorecardButton" style="display: none;">
                            <i class="fas fa-clipboard-list"></i> View Scores
                        </button>
                    <?php endif; ?>
                    -->

                    <!-- Print Button -->
                    <!--
                    <button class="btn btn-print" id="printButton">
                        <i class="fas fa-print"></i> Print
                    </button>
                     -->
                </div>
            </div>
        </div>



        <!-- Flights Container -->
        <div id="flightsContainer">
            <div class="no-records-wrapper">
                <div class="no-records-message">Please select a flight list and round to view draw details.</div>
            </div>
        </div>
    </div>

    <?php include("footer.php"); ?>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>

    <!-- Pass PHP variables to JavaScript -->
    <script>
        var courseIdNameMap = <?php echo json_encode($course_id_name_map); ?>;
        var eventMgmId = '<?php echo $event_mgm_id; ?>';
        var eventIds = <?php echo json_encode($event_ids); ?>;
        var canSeeScorecardButton = <?php echo json_encode($canSeeScorecardButton); ?>;
        var flightListLatestRounds = <?php echo json_encode($flight_list_latest_rounds); ?>;
        var isAdmin = <?php echo ($isAdmin) ? 'true' : 'false'; ?>;
        var userCourseIds = <?php echo json_encode($user_course_ids); ?>; // Array of user's course_ids
    </script>

    <!-- Custom JavaScript -->
    <script>
    $(document).ready(function () {
        let globalPlayerCounter = 1;
        let selectedFlightListLatestRound = null; // To store the latest round number for the selected flight_list_name
        
        // Mobile-specific optimizations
        function isMobile() {
            return window.innerWidth <= 768;
        }
        
        // Add touch-friendly interactions for mobile
        if (isMobile()) {
            // Add touch feedback to buttons
            $('.btn-primary, .btn-secondary, .btn-print, .filter').on('touchstart', function() {
                $(this).addClass('touch-active');
            }).on('touchend touchcancel', function() {
                $(this).removeClass('touch-active');
            });
            
            // Improve table scrolling on mobile
            $('.table-responsive').css({
                'overflow-x': 'auto',
                '-webkit-overflow-scrolling': 'touch'
            });
        }

        // Function to show loader
        function showLoader() {
            $('#flightsContainer').html('<div class="loader">Loading...</div>');
        }

        // Function to hide loader
        function hideLoader() {
            $('.loader').remove();
        }

        // Function to capitalize the first letter
        function ucfirst(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // Helper function to format date as dd Month yyyy (e.g., "15 December 2023")
        function formatDate(dateString) {
            const dateObj = new Date(dateString);
            
            // Check if the date is valid
            if (isNaN(dateObj.getTime())) {
                return 'Invalid Date';
            }
            
            // Extract day and year
            const day = String(dateObj.getDate()).padStart(2, '0');
            const year = dateObj.getFullYear();
            
            // Array of month names
            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            
            // Get the month name
            const monthName = monthNames[dateObj.getMonth()];
            
            // Combine day, month name, and year
            return `${day} ${monthName} ${year}`;
        }   

        // Function to format time to AM/PM
        function formatTimeToAMPM(time) {
            const date = new Date('1970-01-01T' + time);
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'
            minutes = minutes < 10 ? '0' + minutes : minutes;
            return hours + ':' + minutes + ' ' + ampm;
        }

        // Helper function to get country code
        function getCountryCode(countryName) {
            if (!countryName) return 'unknown';
            
            // More comprehensive mapping of countries to ISO codes
            const countryCodes = {
                // ASEAN countries
                'malaysia': 'my',
                'singapore': 'sg',
                'thailand': 'th',
                'indonesia': 'id',
                'philippines': 'ph',
                'vietnam': 'vn',
                'brunei': 'bn',
                'myanmar': 'mm',
                'cambodia': 'kh',
                'laos': 'la',
                
                // European countries
                'switzerland': 'ch',
                'swiss': 'ch',
                'france': 'fr',
                'germany': 'de',
                'italy': 'it',
                'spain': 'es',
                'portugal': 'pt',
                'netherlands': 'nl',
                'belgium': 'be',
                'austria': 'at',
                'greece': 'gr',
                'sweden': 'se',
                'norway': 'no',
                'denmark': 'dk',
                'finland': 'fi',
                'ireland': 'ie',
                'poland': 'pl',
                'czech republic': 'cz',
                'czech': 'cz',
                'hungary': 'hu',
                'romania': 'ro',
                'bulgaria': 'bg',
                'croatia': 'hr',
                'serbia': 'rs',
                'slovenia': 'si',
                'slovakia': 'sk',
                'luxembourg': 'lu',
                'estonia': 'ee',
                'latvia': 'lv',
                'lithuania': 'lt',
                'cyprus': 'cy',
                'malta': 'mt',
                'united kingdom': 'gb',
                'uk': 'gb',
                'england': 'gb',
                'scotland': 'gb',
                'wales': 'gb',
                'northern ireland': 'gb',
                'russia': 'ru',
                'ukraine': 'ua',
                'turkey': 'tr',
                
                // Extended mappings
                'china': 'cn',
                'japan': 'jp',
                'south korea': 'kr',
                'korea': 'kr',
                'taiwan': 'tw',
                'hong kong': 'hk',
                'macau': 'mo',
                'australia': 'au',
                'new zealand': 'nz',
                'india': 'in',
                'pakistan': 'pk',
                'bangladesh': 'bd',
                'sri lanka': 'lk',
                'united states': 'us',
                'usa': 'us',
                'canada': 'ca',
                'brazil': 'br',
                'argentina': 'ar',
                'mexico': 'mx',
                'south africa': 'za',
                'egypt': 'eg',
                'nigeria': 'ng',
                'kenya': 'ke',
                'saudi arabia': 'sa',
                'united arab emirates': 'ae',
                'uae': 'ae',
                'iran': 'ir',
            };
            
            // Normalize country name
            const normalizedCountry = countryName.toLowerCase().trim();
            
            // If country is not found, log to console for debugging
            if (!countryCodes[normalizedCountry]) {
                console.log('Country not found in mappings:', countryName);
            }
            
            // Return the country code or 'unknown' if not found
            return countryCodes[normalizedCountry] || 'unknown';
        }

        // Flight List Name Selection Change
        $('#flightListName').on('change', function () {
            const selectedFlightListName = $(this).val();

            // Get the latest round number for the selected flight_list_name
            selectedFlightListLatestRound = flightListLatestRounds[selectedFlightListName];

            if (selectedFlightListName) {
                $('#roundFilter').prop('disabled', true).html('<option value="" disabled selected>Select Round</option>');

                $.ajax({
                    url: 'fetch_rounds.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        event_ids: JSON.stringify(eventIds),
                        flight_list_name: selectedFlightListName
                    },
                    dataType: 'json',
                    success: function (response) {
                        let rounds;
                        try {
                            if (typeof response === 'string') {
                                rounds = JSON.parse(response);
                            } else {
                                rounds = response;
                            }
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            alert('Failed to fetch rounds.');
                            return;
                        }

                        if (rounds.length > 0) {
                            let options = '<option value="" disabled selected>Select Round</option>';
                            rounds.forEach(function (round) {
                                options += `<option value="${round.round_number}">Round ${round.round_number}</option>`;
                            });
                            $('#roundFilter').html(options).prop('disabled', false);
                        } else {
                            alert('No rounds found for the selected flight list name.');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Failed to fetch rounds:', error);
                        alert('Failed to fetch rounds. Please try again.');
                    }
                });
            }

            // Hide the Scorecard button initially for non-admin users
            if (!isAdmin) {
                $('#scorecardButton').hide();
            }
        });

        // Round Filter Selection Change
        $('#roundFilter').on('change', function () {
            const selectedRound = $(this).val();
            const selectedFlightListName = $('#flightListName').val();

            // Check if the selected round is the latest round for the selected flight_list_name
            let isLatestRound = false;
            if (selectedFlightListLatestRound && selectedRound) {
                isLatestRound = (selectedRound == selectedFlightListLatestRound);
            }

            if (selectedRound && selectedFlightListName) {
                showLoader();

                $.ajax({
                    url: 'fetch_flights.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        event_ids: JSON.stringify(eventIds),
                        round_number: selectedRound,
                        flight_list_name: selectedFlightListName
                    },
                    success: function (response) {
                        hideLoader();
                        let flightData;
                        try {
                            if (typeof response === 'string') {
                                flightData = JSON.parse(response);
                            } else {
                                flightData = response;
                            }
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            $('#flightsContainer').html('<div class="alert alert-danger text-center">Invalid response from server.</div>');
                            // Hide Scorecard button since data is invalid
                            if (!isAdmin) {
                                $('#scorecardButton').hide();
                            }
                            return;
                        }

                        if (flightData.error) {
                            alert(flightData.error);
                            $('#flightsContainer').html('<div class="alert alert-danger text-center">' + flightData.error + '</div>');
                            if (!isAdmin) {
                                $('#scorecardButton').hide();
                            }
                        } else if ((flightData.flights && flightData.flights.length === 0) || (Array.isArray(flightData) && flightData.length === 0)) {
                            $('#flightsContainer').html('<div class="no-records-wrapper"><div class="no-records-message">No flights found.</div></div>');
                            // Hide Scorecard button since no flights match course_id
                            if (!isAdmin) {
                                $('#scorecardButton').hide();
                            }
                        } else {
                            // Extract flight data and display_score from the response
                            const flights = flightData.flights || flightData;
                            const displayScore = Number(flightData.display_score) || 0;
                            
                            // Check if any marker assignments exist
                            const hasMarkerAssignments = flights.some(flight => 
                                flight.marker_name && flight.marker_name !== 'N/A' && flight.marker_name.trim() !== ''
                            );
                            renderFlights(flights, selectedRound, selectedFlightListName, displayScore);

                            // Determine whether to show the Scorecard button
                            if (isAdmin) {
                                // Admin users can always see the Scorecard button if they have access
                                if (canSeeScorecardButton) {
                                    $('#scorecardButton').show();
                                }
                            } else {
                                // Non-admin users can see the Scorecard button only if it's the latest round and flights match course_id
                                const hasMatchingCourse = flights.some(flight => userCourseIds.includes(flight.course_id));
                                if (isLatestRound && canSeeScorecardButton && hasMatchingCourse) {
                                    $('#scorecardButton').show();
                                } else {
                                    $('#scorecardButton').hide();
                                }
                            }
                        }
                    },
                    dataType: 'json',
                    error: function (xhr, status, error) {
                        hideLoader();
                        console.error('Failed to fetch flights:', error);
                        $('#flightsContainer').html('<div class="alert alert-danger text-center">Failed to fetch flight data. Please try again.</div>');
                        // Hide Scorecard button on error
                        if (!isAdmin) {
                            $('#scorecardButton').hide();
                        }
                    }
                });
            }
        });

        // Global variable to store all flight data for search
        let allFlightData = [];
        let currentRoundNumber = null;
        let currentFlightListName = null;
        let currentDisplayScore = 0;

        // Search functionality
        $('#playerSearchBox').on('input', function() {
            const searchTerm = $(this).val().toLowerCase().trim();

            if (allFlightData.length === 0) {
                return; // No data to search
            }

            if (searchTerm === '') {
                // Show all flights if search is empty
                renderFlights(allFlightData, currentRoundNumber, currentFlightListName, currentDisplayScore);
            } else {
                // Filter flights that contain the searched player
                const filteredFlights = allFlightData.filter(flight =>
                    flight.player_name && flight.player_name.toLowerCase().includes(searchTerm)
                );

                renderFlights(filteredFlights, currentRoundNumber, currentFlightListName, currentDisplayScore);
            }
        });

        // Render Flights Function
        function renderFlights(flightData, roundNumber, flightListName, displayScore) {
            displayScore = Number(displayScore); // Ensure numeric
            console.log('Rendering table, displayScore:', displayScore, typeof displayScore);
            console.log('Flight Data:', flightData); // For debugging

            // Store data globally for search functionality (only if this is the initial load)
            if (arguments.length === 4 && $('#playerSearchBox').val() === '') {
                allFlightData = [...flightData];
                currentRoundNumber = roundNumber;
                currentFlightListName = flightListName;
                currentDisplayScore = displayScore;
            }

            const flightsContainer = $('#flightsContainer');
            flightsContainer.empty();

            if (flightData.length === 0) {
                flightsContainer.append('<div class="no-records-wrapper"><div class="no-records-message">No records found.</div></div>');
                return;
            }

            // Scan the entire data for a real marker
            const hasMarkerAssignments = flightData.some(flight => 
                flight.marker_name && flight.marker_name !== 'N/A' && flight.marker_name.trim() !== ''
            );

            // Get the flight date from the first flight
            const flightDate = flightData[0].flight_date;
            const formattedDate = formatDate(flightDate);

            // Group flights by their start point
            const groupedFlightsByStartPoint = {};
            flightData.forEach(flight => {
                if (!groupedFlightsByStartPoint[flight.start_point]) {
                    groupedFlightsByStartPoint[flight.start_point] = [];
                }
                groupedFlightsByStartPoint[flight.start_point].push(flight);
            });

            // Loop through each start point and render the flights
            for (const startPoint in groupedFlightsByStartPoint) {
                const flights = groupedFlightsByStartPoint[startPoint];
                const groupedFlights = {};

                // Gather unique course names for this group of flights
                const courseNames = new Set();
                flights.forEach(flight => {
                    const courseId = String(flight.course_id); // Convert to string for lookup
                    const courseName = courseIdNameMap[courseId] || 'Unknown Course';
                    courseNames.add(courseName);
                });

                // Convert the Set to an array and join with commas to display all course names
                const displayedCourseNames = Array.from(courseNames).join(', ');

                console.log('Looking up Course IDs:', Array.from(courseNames)); // Debugging line
                console.log('Course Names found:', displayedCourseNames); // Debugging line

                // Display headers with new professional styling
                flightsContainer.append(`
                    <div class="section-header">
                        <h3 class="start-header">Round ${roundNumber} - ${ucfirst(startPoint)} Start</h3>
                        <div class="flight-date">Date: ${formattedDate}</div>
                        <div class="course-name">Course: ${displayedCourseNames}</div>
                    </div>
                `);

                // Group flights by flight_name
                flights.forEach(flight => {
                    if (!groupedFlights[flight.flight_name]) {
                        groupedFlights[flight.flight_name] = [];
                    }
                    groupedFlights[flight.flight_name].push(flight);
                });

                // Call rendering functions based on start point
                const normalizedStartPoint = startPoint.trim().toLowerCase();
                if (normalizedStartPoint.includes('shotgun')) {
                    renderShotgunGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore);
                } else if (normalizedStartPoint.includes('one point')) {
                    renderOnePointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore);
                } else if (normalizedStartPoint.includes('two point')) {
                    renderTwoPointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore);
                } else if (normalizedStartPoint.includes('four point')) {
                    renderFourPointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore);
                } else if (normalizedStartPoint.includes('customize')) {
                    renderCustomizeGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore);
                } else {
                    console.error('Unknown start point:', startPoint);
                }
            }
        }

        function renderShotgunGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore = false) {
            console.log('renderShotgunGroupBoxes displayScore:', displayScore, typeof displayScore);
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');
            let playerNumber = 1; // Global counter for numbering players

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightId in groupedFlights) {
                const flightDetails = groupedFlights[flightId];
                const flightInfo = flightDetails[0]; // Use the first record for tee box info
                const teeBox = flightInfo.tee_box || 'N/A';
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }
                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time,
                });
            }

            // Sort TeeBoxes numerically by extracting any number from the string
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const numA = parseInt(a.replace(/\D/g, ''), 10);
                const numB = parseInt(b.replace(/\D/g, ''), 10);
                return numA - numB;
            });

            // Render each TeeBox's flights
            sortedTeeBoxes.forEach((teeBox, index) => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append('<h5>' + teeBox + '</h5>');

                teeFlights.forEach((flight) => {
                    flightDetailDiv.append('<h6><strong>' + flight.flightName + ' (Time: ' + formatTimeToAMPM(flight.flightTime) + ')</strong></h6>');

                    const table = $(`
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>Player Name</th>
                                        ${displayScore === 1 ? '<th>Score</th>' : ''}
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');
                    flight.flightDetails.forEach((player) => {
                        // Display flag if country is available, otherwise show player number
                        let flagDisplay;
                        if (player.country) {
                            const countryCode = getCountryCode(player.country);
                            if (countryCode === 'unknown') {
                                // Show the country name in italics if code not found
                                flagDisplay = `<span class="unknown-flag" title="Country code not found: ${player.country}">${player.country.substring(0, 2).toUpperCase()}</span>`;
                            } else {
                                flagDisplay = `<span class="flag-icon flag-icon-${countryCode}" title="${player.country}"></span>`;
                            }
                        } else {
                            flagDisplay = `<span class="player-number">${playerNumber}</span>`;
                        }
                        
                        tbody.append(`
                            <tr>
                                <td>${flagDisplay}</td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                ${displayScore === 1 ? `<td>${player.total_strokes || ''}</td>` : ''}
                                <td>${player.category || 'N/A'}</td>
                                ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                            </tr>
                        `);
                        playerNumber++; // Increment global player number
                    });
                    flightDetailDiv.append(table);
                });

                // Use the middle index to split tee boxes evenly between two columns
                const middleIndex = Math.ceil(sortedTeeBoxes.length / 2);
                if (index < middleIndex) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Render One Point group boxes
        function renderOnePointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore) {
            const col1 = $('<div class="col-lg-12 mb-3"></div>');  // Single column for One Point
            let playerNumber = 1;  // Global player number for all flights

            // Sort flightIds in numerical order based on flight_name (e.g., "FLT 1", "FLT 2", "FLT 10")
            const sortedFlightIds = Object.keys(groupedFlights).sort((a, b) => {
                const numberA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const numberB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return numberA - numberB;
            });

            // Iterate through each sorted flight and display in one column
            sortedFlightIds.forEach((flightId) => {
                const flightDetails = groupedFlights[flightId];
                const flightInfo = flightDetails[0];

                const flightDetailDiv = $(
                    '<div class="flight-detail">' +
                        '<h5>TeeBox ' + flightInfo.tee_box + '</h5>' +  // TeeBox heading
                        '<h6><strong>Flight ' + flightInfo.flight_name + ' (Time: ' + formatTimeToAMPM(flightInfo.flight_time) + ')</strong></h6>' +
                        '<div class="table-responsive">' +
                            '<table class="table">' +
                                '<thead>' +
                                    '<tr>' +
                                        '<th></th>' +
                                        '<th>Player Name</th>' +
                                        (displayScore ? '<th>Score</th>' : '') +
                                        '<th>Cat</th>' +
                                    '</tr>' +
                                '</thead>' +
                                '<tbody></tbody>' +
                            '</table>' +
                        '</div>' +
                    '</div>'
                );

                const tbody = flightDetailDiv.find('tbody');
                flightDetails.forEach((flight) => {
                    // Display flag if country is available, otherwise show player number
                    let flagDisplay;
                    if (flight.country) {
                        const countryCode = getCountryCode(flight.country);
                        flagDisplay = `<span class="flag-icon flag-icon-${countryCode}" title="${flight.country}"></span>`;
                    } else {
                        flagDisplay = `<span class="player-number">${playerNumber}</span>`;
                    }
                    
                    tbody.append(
                        '<tr>' +
                            '<td>' + flagDisplay + '</td>' +
                            '<td>' + (flight.player_name || 'No player assigned') + '</td>' +
                            (displayScore === 1 ? '<td>' + (flight.total_strokes || '') + '</td>' : '') +
                            '<td>' + (flight.category || 'N/A') + '</td>' +
                            (hasMarkerAssignments ? `<td class="marker-col">${flight.marker_name || 'N/A'}</td>` : '') +
                        '</tr>'
                    );
                    playerNumber++;
                });

                col1.append(flightDetailDiv);
            });

            // Create a row containing the single column
            const row = $('<div class="row"></div>');
            row.append(col1);
            $('#flightsContainer').append(row);
        }

        // Render Two Point group boxes
        function renderTwoPointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');
            let playerNumber = 1;  // Initialize global player number

            // Sort flightIds in numerical order based on flight name (e.g., A1, B1)
            const sortedFlightIds = Object.keys(groupedFlights).sort((a, b) => {
                const numberA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const numberB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return numberA - numberB;
            });
            // ------------------------------------

            // Group flights by TeeBox using sortedFlightIds
            const groupedByTeeBox = {};
            sortedFlightIds.forEach(flightId => {  // Use sortedFlightIds for iteration
                const flightDetails = groupedFlights[flightId];
                const flightInfo = flightDetails[0];  // Get the first record for flight details

                const teeBox = flightInfo.tee_box;
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }

                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time
                });
            });

            // Sort TeeBoxes in numerical order
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const teeBoxA = parseInt(a.replace('TeeBox ', ''), 10);
                const teeBoxB = parseInt(b.replace('TeeBox ', ''), 10);
                return teeBoxA - teeBoxB;
            });

            // Iterate through each sorted TeeBox and render flights
            sortedTeeBoxes.forEach((teeBox, index) => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append('<h5>' + teeBox + '</h5>');

                teeFlights.forEach((flight) => {
                    // Display flight name and time below the TeeBox title, bolded
                    flightDetailDiv.append('<h6><strong>' + flight.flightName + ' (Time: ' + formatTimeToAMPM(flight.flightTime) + ')</strong></h6>');

                    const table = $(
                        '<div class="table-responsive">' +
                            '<table class="table">' +
                                '<thead>' +
                                    '<tr>' +
                                        '<th></th>' +
                                        '<th>Player Name</th>' +
                                        (displayScore === 1 ? '<th>Score</th>' : '') +
                                        '<th>Cat</th>' +
                                        (hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : '') +
                                    '</tr>' +
                                '</thead>' +
                                '<tbody></tbody>' +
                            '</table>' +
                        '</div>'
                    );

                    let tbody = table.find('tbody');

                    // List each player within this flight
                    flight.flightDetails.forEach((player) => {
                        // Display flag if country is available, otherwise show player number
                        let flagDisplay;
                        if (player.country) {
                            const countryCode = getCountryCode(player.country);
                            if (countryCode === 'unknown') {
                                // Show the country name in italics if code not found
                                flagDisplay = `<span class="unknown-flag" title="Country code not found: ${player.country}">${player.country.substring(0, 2).toUpperCase()}</span>`;
                            } else {
                                flagDisplay = `<span class="flag-icon flag-icon-${countryCode}" title="${player.country}"></span>`;
                            }
                        } else {
                            flagDisplay = `<span class="player-number">${playerNumber}</span>`;
                        }
                        
                        tbody.append(
                            '<tr>' +
                                '<td>' + flagDisplay + '</td>' +
                                '<td>' + (player.player_name || 'No player assigned') + '</td>' +
                                (displayScore === 1 ? '<td>' + (player.total_strokes || '') + '</td>' : '') +
                                '<td>' + (player.category || 'N/A') + '</td>' +
                                (hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : '') +
                            '</tr>'
                        );
                        playerNumber++; // Increment player number globally
                    });

                    flightDetailDiv.append(table);
                });

                // Assign TeeBoxes to alternating columns
                const middleIndex = Math.ceil(sortedTeeBoxes.length / 2);
                if (index < middleIndex) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Render Four Point group boxes
        function renderFourPointGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');
            let playerNumber = 1; // Global counter for numbering players

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightId in groupedFlights) {
                const flightDetails = groupedFlights[flightId];
                const flightInfo = flightDetails[0]; // Use the first record for tee box info
                const teeBox = flightInfo.tee_box || 'N/A';
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }
                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time,
                });
            }

            // Sort TeeBoxes numerically by extracting any number from the string
            // Handle different teebox name formats (e.g., "Tee 9", "TeeBox 9", "9")
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                // Extract numbers from teebox names, handling various formats
                const numA = parseInt(a.replace(/[^\d]/g, ''), 10) || 0;
                const numB = parseInt(b.replace(/[^\d]/g, ''), 10) || 0;
                return numA - numB;
            });

            // Render each TeeBox's flights
            sortedTeeBoxes.forEach((teeBox, index) => {
                let teeFlights = groupedByTeeBox[teeBox];
                // Sort teeFlights by time (earliest first)
                teeFlights = teeFlights.slice().sort((a, b) => {
                    // a.flightTime and b.flightTime are in 'HH:mm' or 'HH:mm:ss' format
                    const timeA = a.flightTime.length === 5 ? a.flightTime + ':00' : a.flightTime;
                    const timeB = b.flightTime.length === 5 ? b.flightTime + ':00' : b.flightTime;
                    return timeA.localeCompare(timeB);
                });
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append('<h5>' + teeBox + '</h5>');

                teeFlights.forEach((flight) => {
                    flightDetailDiv.append('<h6><strong>' + flight.flightName + ' (Time: ' + formatTimeToAMPM(flight.flightTime) + ')</strong></h6>');

                    const table = $(`
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>Player Name</th>
                                        ${displayScore === 1 ? '<th>Score</th>' : ''}
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');
                    flight.flightDetails.forEach((player) => {
                        // Display flag if country is available, otherwise show player number
                        let flagDisplay;
                        if (player.country) {
                            const countryCode = getCountryCode(player.country);
                            if (countryCode === 'unknown') {
                                // Show the country name in italics if code not found
                                flagDisplay = `<span class="unknown-flag" title="Country code not found: ${player.country}">${player.country.substring(0, 2).toUpperCase()}</span>`;
                            } else {
                                flagDisplay = `<span class="flag-icon flag-icon-${countryCode}" title="${player.country}"></span>`;
                            }
                        } else {
                            flagDisplay = `<span class="player-number">${playerNumber}</span>`;
                        }
                        
                        tbody.append(`
                            <tr>
                                <td>${flagDisplay}</td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                ${displayScore === 1 ? `<td>${player.total_strokes || ''}</td>` : ''}
                                <td>${player.category || 'N/A'}</td>
                                ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                            </tr>
                        `);
                        playerNumber++; // Increment global player number
                    });
                    flightDetailDiv.append(table);
                });

                // Assign to columns based on index for 4-point start
                // First two teeboxes go to left column, last two to right column
                if (index < 2) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Render Customize group boxes
        function renderCustomizeGroupBoxes(groupedFlights, hasMarkerAssignments, displayScore) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');  // Left column
            const col2 = $('<div class="col-lg-6 mb-3"></div>');  // Right column
            let playerNumber = 1;  // Initialize player number counter
            let index = 0;  // Index counter for assigning columns

            // Iterate through each flight and display in a top-down order
            for (const flightId in groupedFlights) {
                const flightDetails = groupedFlights[flightId];
                const flightInfo = flightDetails[0];

                const flightDetailDiv = $(
                    '<div class="flight-detail">' +
                        '<h5>TeeBox ' + flightInfo.tee_box + '</h5>' +  // TeeBox heading
                        '<h6><strong>Flight ' + flightInfo.flight_name + ' (Time: ' + formatTimeToAMPM(flightInfo.flight_time) + ')</strong></h6>' +
                        '<div class="table-responsive">' +
                            '<table class="table">' +
                                '<thead>' +
                                    '<tr>' +
                                        '<th></th>' +
                                        '<th>Player Name</th>' +
                                        (displayScore === 1 ? '<th>Score</th>' : '') +
                                        '<th>Cat</th>' +
                                    '</tr>' +
                                '</thead>' +
                                '<tbody></tbody>' +
                            '</table>' +
                        '</div>' +
                    '</div>'
                );

                const tbody = flightDetailDiv.find('tbody');
                flightDetails.forEach((flight) => {
                    // Display flag if country is available, otherwise show player number
                    let flagDisplay;
                    if (flight.country) {
                        const countryCode = getCountryCode(flight.country);
                        if (countryCode === 'unknown') {
                            // Show the country name in italics if code not found
                            flagDisplay = `<span class="unknown-flag" title="Country code not found: ${flight.country}">${flight.country.substring(0, 2).toUpperCase()}</span>`;
                        } else {
                            flagDisplay = `<span class="flag-icon flag-icon-${countryCode}" title="${flight.country}"></span>`;
                        }
                    } else {
                        flagDisplay = `<span class="player-number">${playerNumber}</span>`;
                    }
                    
                    tbody.append(
                        '<tr>' +
                            '<td>' + flagDisplay + '</td>' +
                            '<td>' + (flight.player_name || 'No player assigned') + '</td>' +
                            (displayScore === 1 ? '<td>' + (flight.total_strokes || '') + '</td>' : '') +
                            '<td>' + (flight.category || 'N/A') + '</td>' +
                            (hasMarkerAssignments ? `<td class="marker-col">${flight.marker_name || 'N/A'}</td>` : '') +
                        '</tr>'
                    );
                    playerNumber++; // Increment player number counter
                });

                // Assign flightDetailDiv to columns alternately for a balanced layout
                if (index % 2 === 0) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
                index++;
            }

            // Create a row containing two columns
            const row = $('<div class="row"></div>');
            row.append(col1).append(col2);
            $('#flightsContainer').append(row);
        }

        // Scorecard button functionality
        $('#scorecardButton').click(function () {
            let selectedRound = $('#roundFilter').val();
            let selectedFlightListName = $('#flightListName').val();

            if (!selectedRound || !selectedFlightListName) {
                alert('Please select both round and flight list name before viewing the scorecard.');
                return;
            }

            const scorecardUrl = 'admin_scores.php?event_mgm_id=' + encodeURIComponent(eventMgmId) + 
                                 '&round_number=' + encodeURIComponent(selectedRound) + 
                                 '&flight_list_name=' + encodeURIComponent(selectedFlightListName);
            window.location.href = scorecardUrl;
        });

        // Print button functionality
        $('#printButton').click(function() {
            let selectedRound = $('#roundFilter').val();
            let selectedFlightListName = $('#flightListName').val();

            if (!selectedRound || !selectedFlightListName) {
                alert('Please select both round and flight list name before printing.');
                return;
            }
            
            // Get current date and time
            const now = new Date();
            const dateTimeString = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();
            
            // Set the print header details
            $('#printEventDetails').text('Flight List: ' + selectedFlightListName + ' - Round ' + selectedRound);
            $('#printDateTime').text('Generated on: ' + dateTimeString);
            
            // Hide header and footer for printing
            $('.header-section, header, footer, .header-controls').addClass('d-none');
            
            // Add a print-specific class to the body
            $('body').addClass('printing');
            
            // Print the page
            window.print();
            
            // Remove the print classes when done
            setTimeout(function() {
                $('.header-section, header, footer, .header-controls').removeClass('d-none');
                $('body').removeClass('printing');
            }, 1000);
        });

        // Mobile-specific enhancements
        if (isMobile()) {
            // Add smooth scrolling for better mobile experience
            $('html').css('scroll-behavior', 'smooth');
            
            // Add pull-to-refresh prevention
            let startY = 0;
            let currentY = 0;
            
            document.addEventListener('touchstart', function(e) {
                startY = e.touches[0].pageY;
            }, { passive: true });
            
            document.addEventListener('touchmove', function(e) {
                currentY = e.touches[0].pageY;
                if (window.scrollY === 0 && currentY > startY) {
                    e.preventDefault();
                }
            }, { passive: false });
            
            // Optimize table scrolling on mobile
            $('.table-responsive').each(function() {
                $(this).on('touchstart', function() {
                    $(this).addClass('scrolling');
                }).on('touchend', function() {
                    setTimeout(() => {
                        $(this).removeClass('scrolling');
                    }, 100);
                });
            });

            // Mobile-friendly alert replacement
            window.mobileAlert = function(message) {
                const alertDiv = $('<div class="mobile-alert">' + message + '</div>');
                $('body').append(alertDiv);
                setTimeout(() => {
                    alertDiv.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            };

            // Replace standard alerts with mobile-friendly ones
            window.alert = window.mobileAlert;
        }

        // Add search/filter functionality for player name
        $(document).ready(function () {
            // ... existing code ...
            // Player name search filter (show only matching TeeBox group)
            $(document).on('input', '#playerSearchBox', function() {
                const search = $(this).val().toLowerCase();
                // For each flight-detail group (TeeBox)
                $('#flightsContainer .flight-detail').each(function() {
                    let groupHasMatch = false;
                    // For each player row in this group
                    $(this).find('tbody tr').each(function() {
                        const nameCell = $(this).find('td').eq(1); // Player Name is 2nd column
                        const playerName = nameCell.text().toLowerCase();
                        if (search === '' || playerName.indexOf(search) !== -1) {
                            $(this).show();
                            groupHasMatch = true;
                            // Highlight match
                            if (search !== '') {
                                const regex = new RegExp('(' + search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'ig');
                                nameCell.html(nameCell.text().replace(regex, '<mark>$1</mark>'));
                            } else {
                                nameCell.html(nameCell.text());
                            }
                        } else {
                            $(this).hide();
                        }
                    });
                    // Show/hide the whole group based on match
                    if (groupHasMatch) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        });
    });
    </script>
</body>
</html>
