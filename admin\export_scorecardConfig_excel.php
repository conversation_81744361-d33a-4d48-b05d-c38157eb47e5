<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

require_once __DIR__ . '/MAILER/vendor/autoload.php';
require_once __DIR__ . '/../database.php'; // Adjust path if needed

// Get event_mgm_id from GET
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
if (!$event_mgm_id) {
    die('Event Management ID not provided.');
}

// Fetch event name
$stmt = $conn->prepare("SELECT event_name FROM event_mgm WHERE event_mgm_id = ?");
$stmt->bind_param("s", $event_mgm_id);
$stmt->execute();
$result = $stmt->get_result();
$event = $result->fetch_assoc();
$event_name = $event ? $event['event_name'] : 'Unknown Event';
$stmt->close();

// Fetch scorecard data, order by course then scorecard_id
$scorecard_query = "
    SELECT sc.scorecard_id, sc.event_mgm_id, sc.course_id, c.course_name,
           sc.hole_1, sc.hole_2, sc.hole_3, sc.hole_4, sc.hole_5, sc.hole_6,
           sc.hole_7, sc.hole_8, sc.hole_9, sc.hole_10, sc.hole_11, sc.hole_12,
           sc.hole_13, sc.hole_14, sc.hole_15, sc.hole_16, sc.hole_17, sc.hole_18,
           sc.name, sc.otp
    FROM scorecard_configuration sc
    LEFT JOIN course_info c ON sc.course_id = c.course_id
    WHERE sc.event_mgm_id = ?
    ORDER BY c.course_name ASC, sc.scorecard_id ASC
";
$stmt_scorecard = $conn->prepare($scorecard_query);
$stmt_scorecard->bind_param("s", $event_mgm_id);
$stmt_scorecard->execute();
$scorecard_result = $stmt_scorecard->get_result();

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$row = 1;

// Event name at the top
$sheet->setCellValue('A'.$row, $event_name);
$sheet->mergeCells('A1:E1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
$row += 2;

$current_course = '';
$index = 1;
while ($data = $scorecard_result->fetch_assoc()) {
    // If new course, print header
    if ($data['course_name'] !== $current_course) {
        $current_course = $data['course_name'];
        $sheet->setCellValue('A'.$row, 'No.');
        $sheet->setCellValue('B'.$row, 'Golf Course');
        $sheet->setCellValue('C'.$row, 'Name');
        $sheet->setCellValue('D'.$row, 'OTP');
        $sheet->setCellValue('E'.$row, 'Holes');
        $sheet->getStyle('A'.$row.':E'.$row)->getFont()->setBold(true);
        $sheet->getStyle('A'.$row.':E'.$row)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $sheet->getStyle('A'.$row.':E'.$row)->getFill()->getStartColor()->setRGB('E2E8F0');
        $row++;
        $index = 1;
    }
    // Gather selected holes
    $selected_holes = [];
    for ($i = 1; $i <= 18; $i++) {
        if (isset($data["hole_$i"]) && $data["hole_$i"] == 1) {
            $selected_holes[] = $i;
        }
    }
    $holes_display = !empty($selected_holes) ? implode(", ", $selected_holes) : 'None';
    $sheet->setCellValue('A' . $row, $index);
    $sheet->setCellValue('B' . $row, $data['course_name']);
    $sheet->setCellValue('C' . $row, $data['name']);
    $sheet->setCellValue('D' . $row, $data['otp']);
    $sheet->setCellValue('E' . $row, $holes_display);
    $row++;
    $index++;
}
// Auto-size columns
foreach (range('A', 'E') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}
$stmt_scorecard->close();

// Set headers for download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
header('Content-Disposition: attachment;filename="Scorecard_Config_' . $event_name . '_' . date('Y-m-d') . '.xlsx"');
header('Cache-Control: max-age=0');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit(); 