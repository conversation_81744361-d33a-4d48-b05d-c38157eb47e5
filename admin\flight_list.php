<?php
session_start();
require("../database.php");

// Fetch the event_mgm_id from the URL
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
if (!$event_mgm_id) {
    die('Event Management ID not provided.');
}
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as total_records, 
                     SUM(CASE WHEN event_mgm_id = ? THEN 1 ELSE 0 END) as match_event 
              FROM event_role_mgm 
              WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $event_mgm_id, $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($total_records, $match_event);
    $stmt->fetch();
    $stmt->close();

    if ($total_records > 0) {
        // Profile ID is registered in event_role_mgm
        if ($match_event == 0 || $admin_roleid != 'gPHOfKV0sL') {
            // If event_mgm_id doesn't match or role is not gPHOfKV0sL
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } else {
        // Profile ID not registered in event_role_mgm
        if ($admin_roleid != 'gPHOfKV0sL') {
            // If role is not gPHOfKV0sL, deny access
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch all event_id associated with the given event_mgm_id
$event_query = "
    SELECT em.event_id, em.event_num_participant
    FROM event_mgm AS em
    WHERE em.event_mgm_id = ?";
$stmt_event = $conn->prepare($event_query);
$stmt_event->bind_param("s", $event_mgm_id);
$stmt_event->execute();
$event_result = $stmt_event->get_result();

if ($event_result->num_rows == 0) {
    die('Invalid event_mgm_id or no event found.');
}

$event_ids = [];
$event_num_participant = null;
while ($event = $event_result->fetch_assoc()) {
    $event_ids[] = $event['event_id'];
    // Assuming event_num_participant is the same for all event_id under the same event_mgm_id
    if (is_null($event_num_participant)) {
        $event_num_participant = $event['event_num_participant'];
    }
}

// Prepare placeholders and types for IN clause
$in_placeholders = implode(',', array_fill(0, count($event_ids), '?'));
$types = str_repeat('s', count($event_ids));

// Fetch course_id(s) from custom_hole table
$course_query = "
    SELECT DISTINCT course_id
    FROM custom_hole
    WHERE event_id IN ($in_placeholders)";
$stmt_course = $conn->prepare($course_query);
$stmt_course->bind_param($types, ...$event_ids);
$stmt_course->execute();
$course_result = $stmt_course->get_result();

$course_ids = [];
if ($course_result->num_rows > 0) {
    while ($course = $course_result->fetch_assoc()) {
        $course_ids[] = $course['course_id'];
    }
}

// Debugging: Log the fetched course IDs
error_log("Fetched Course IDs: " . print_r($course_ids, true));

// Fetch all course_ids and their names into an associative array
$course_id_name_map = [];
if (count($course_ids) > 0) {
    $course_in_placeholders = implode(',', array_fill(0, count($course_ids), '?'));
    $course_types = str_repeat('s', count($course_ids));

    $course_name_query = "
        SELECT course_id, course_name
        FROM course_info
        WHERE course_id IN ($course_in_placeholders)";
    $stmt_course_name = $conn->prepare($course_name_query);
    $stmt_course_name->bind_param($course_types, ...$course_ids);
    $stmt_course_name->execute();
    $course_name_result = $stmt_course_name->get_result();

    while ($course_info = $course_name_result->fetch_assoc()) {
        $course_id_name_map[$course_info['course_id']] = $course_info['course_name'];
    }
}

// Debugging: Log the course ID to name mappings
error_log("Course ID to Name Map: " . print_r($course_id_name_map, true));

// Fetch distinct flight_list_names for the dropdown
$flight_list_name_query = "
    SELECT DISTINCT flight_list_name
    FROM flight_list
    WHERE event_id IN ($in_placeholders)
    ORDER BY flight_list_name ASC";
$stmt_flight_list_name = $conn->prepare($flight_list_name_query);
$stmt_flight_list_name->bind_param($types, ...$event_ids);
$stmt_flight_list_name->execute();
$flight_list_name_result = $stmt_flight_list_name->get_result();

$flight_list_names = [];
if ($flight_list_name_result) {
    while ($row = $flight_list_name_result->fetch_assoc()) {
        $flight_list_names[] = $row['flight_list_name'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Draw List</title>
    
    <!-- Tailwind + Bootstrap -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Flag Icon CSS (for country flags) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.6/css/flag-icon.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* 1. Global Font Size Adjustment */
        body {
            background-color: #f8fafc;
            font-family: 'Inter', 'Segoe UI', sans-serif;
            font-size: 0.875rem;
            color: #374151;
        }

        /* 2. Main Title Styling */
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 24px 0;
            text-align: center;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        /* 3. Professional Card Layout */
        .control-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin-bottom: 24px;
        }

        /* 4. Header Controls Styling */
        .header-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .filter-section {
            display: flex;
            gap: 16px;
            align-items: center;
            justify-content: center;
            width: 100%;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-control {
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            background: #ffffff;
            transition: border-color 0.2s, box-shadow 0.2s;
            min-width: 200px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 5. Action Menu Styling */
        .action-menu {
            position: relative;
            display: inline-block;
        }

        .action-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }

        .action-btn:hover {
            background: #2563eb;
        }

        .action-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            display: none;
            margin-top: 4px;
        }

        .action-dropdown.show {
            display: block;
        }

        .action-dropdown a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: #374151;
            text-decoration: none;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s;
        }

        .action-dropdown a:last-child {
            border-bottom: none;
        }

        .action-dropdown a:hover {
            background: #f9fafb;
        }

        .action-dropdown i {
            width: 16px;
            color: #6b7280;
        }

        /* Flag styling */
        .flag-icon {
            width: 20px;
            height: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Status indicators */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fef2f2;
            color: #991b1b;
        }

        /* Loading states */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 6. Flight Detail Box Styling */
        .flight-detail {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 20px;
            padding: 20px;
            font-size: 0.875rem;
        }

        .flight-detail h5 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }

        .flight-detail h6 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #4b5563;
            margin-top: 16px;
            margin-bottom: 16px;
            text-align: center;
        }

        /* 7. No Records Message Styling */
        .no-records-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            background-color: #f9fafb;
            border-radius: 12px;
            border: 2px dashed #d1d5db;
            font-size: 0.875rem;
        }

        .no-records-message {
            color: #6b7280;
            font-weight: 500;
        }

        /* 8. Professional Table Styling */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: white;
        }

        table th {
            background: #f9fafb;
            color: #374151;
            font-weight: 600;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            vertical-align: middle;
        }

        table tbody tr:hover {
            background-color: #f9fafb;
        }

        table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Center align specific columns */
        table th:nth-child(3),
        table td:nth-child(3),
        table th:nth-child(4),
        table td:nth-child(4) {
            text-align: center;
        }

        /* 11. Column Widths (Optimized for shorter headers) */
        table th:nth-child(1),
        table td:nth-child(1) {
            width: 8%; /* Flag */
        }
        table th:nth-child(2),
        table td:nth-child(2) {
            width: 45%; /* Player Name - increased */
        }
        table th:nth-child(3),
        table td:nth-child(3) {
            width: 12%; /* HCP/Score */
        }
        table th:nth-child(4),
        table td:nth-child(4) {
            width: 15%; /* Cat - reduced */
        }
        table th:nth-child(5),
        table td:nth-child(5) {
            width: 20%; /* Mkr */
        }

        /* 12. Additional Classes */
        .text-center {
            text-align: center;
        }

        /* 13. Headings Inside flight-detail */
        .flight-detail h5 {
            font-size: 1rem;
            text-align: center;
        }
        .flight-detail h6 {
            font-size: 0.75rem;
            text-align: center;
        }

        /* 14. Loader and Alert Font Sizes */
        .loader {
            font-size: 0.75rem;
        }
        .alert {
            font-size: 0.75rem;
        }

        /* 15. Hover Effects for Table Rows */
        table tr:hover {
            background-color: #f1f1f1;
        }

        /* 9. Professional Section Headers */
        .section-header {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 24px;
            margin: 32px 0 24px 0;
            text-align: center;
        }

        .start-header {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .flight-date {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .course-name {
            font-size: 1rem;
            color: #4b5563;
            font-weight: 500;
        }

        /* 10. Responsive Design */
        @media (max-width: 768px) {
            .header-controls {
                flex-direction: column;
                gap: 16px;
            }

            .filter-section {
                flex-direction: column;
                gap: 12px;
                justify-content: center;
            }

            .form-control {
                min-width: 100%;
            }

            .action-menu {
                width: 100%;
            }

            .action-btn {
                width: 100%;
                justify-content: center;
            }

            .action-dropdown {
                left: 0;
                right: 0;
            }

            .flight-detail {
                padding: 16px;
            }

            .section-header {
                padding: 16px;
            }

            .main-title {
                font-size: 1.5rem;
            }
        }

        /* 18. Print Adjustments */
        @media print {
            a {
                text-decoration: none;
                color: black;
            }
            a[href]:after,
            a[href]:before {
                content: none !important;
            }
            @page {
                margin: 0;
            }
            .start-header {
                font-size: 2rem !important;
            }
            .course-name {
                font-size: 1.5rem !important;
            }
        }

        @media (max-width: 600px) {
            .control-card {
                padding: 16px;
            }

            .filter-section {
                gap: 8px;
            }

            .form-group label {
                font-size: 0.7rem;
            }

            table th, table td {
                font-size: 0.75rem !important;
                padding: 8px 6px !important;
            }

            table th {
                padding: 10px 6px !important;
            }

            .flight-detail {
                padding: 12px !important;
            }

            .flight-detail h5 {
                font-size: 1rem;
            }

            .flight-detail h6 {
                font-size: 0.8rem;
            }

            /* Keep marker column visible on mobile */

            .section-header {
                padding: 12px;
            }

            .start-header {
                font-size: 1.25rem;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <?php include("admin_header.php"); ?>

    <div class="header-section">
        <h2 class="main-title">Draw List</h2>
    </div>

    <div class="container">
        <div class="control-card">
            <div class="header-controls">
                <div class="filter-section">
                    <!-- Flight List Name Dropdown -->
                    <div class="form-group">
                        <label for="flightListName">Flight List</label>
                        <select class="form-control" id="flightListName" name="flight_list_name">
                            <option value="" disabled selected>Select Flight List Name</option>
                            <?php foreach ($flight_list_names as $name): ?>
                                <option value="<?php echo htmlspecialchars($name); ?>">
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Round Filter Dropdown -->
                    <div class="form-group">
                        <label for="roundFilter">Round</label>
                        <select class="form-control" id="roundFilter" disabled>
                            <option value="" disabled selected>Select Round</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>

                <!-- Action Menu -->
                <div class="action-menu">
                    <button class="action-btn" id="actionMenuBtn">
                        <i class="fas fa-cog"></i>
                        Actions
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="action-dropdown" id="actionDropdown">
                        <a href="draw_list.php?event_mgm_id=<?php echo htmlspecialchars($event_mgm_id); ?>">
                            <i class="fas fa-plus"></i>
                            Add Flights
                        </a>
                        <a href="edit_flight.php?event_mgm_id=<?php echo htmlspecialchars($event_mgm_id); ?>">
                            <i class="fas fa-edit"></i>
                            Edit Flights
                        </a>
                        <a href="#" id="printButton">
                            <i class="fas fa-print"></i>
                            Print List
                        </a>
                        <a href="#" id="printScorecardButton">
                            <i class="fas fa-id-card"></i>
                            Print Scorecard Labels
                        </a>
                        <a href="#" id="exportButton">
                            <i class="fas fa-file-export"></i>
                            Export Pace Tracking
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flights Container -->
        <div id="flightsContainer">
            <div class="no-records-wrapper">
                <div class="no-records-message">No records found.</div>
            </div>
        </div>
    </div>

    <?php include("admin_footer.php"); ?>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>

    <script>
        var courseIdNameMap = <?php echo json_encode($course_id_name_map); ?>;
        console.log("Course ID Name Map in JS:", courseIdNameMap);
        var eventMgmId = '<?php echo $event_mgm_id; ?>';
        var eventIds = <?php echo json_encode($event_ids); ?>;

        // Action menu dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const actionBtn = document.getElementById('actionMenuBtn');
            const actionDropdown = document.getElementById('actionDropdown');

            actionBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                actionDropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                actionDropdown.classList.remove('show');
            });

            // Prevent dropdown from closing when clicking inside
            actionDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>

    <!-- Custom JavaScript -->
    <script>
    $(document).ready(function () {

        // ========= 1) COUNTRY -> FLAG MAPPING =========
        // This matches your print_flight.php approach.
        function getCountryCode(countryName) {
            const countryCodes = {
                'AFGHANISTAN': 'af',
                'ALBANIA': 'al',
                'ALGERIA': 'dz',
                'ANDORRA': 'ad',
                'ANGOLA': 'ao',
                'ANTIGUA AND BARBUDA': 'ag',
                'ARGENTINA': 'ar',
                'ARMENIA': 'am',
                'AUSTRALIA': 'au',
                'AUSTRIA': 'at',
                'AZERBAIJAN': 'az',
                'BAHAMAS': 'bs',
                'BAHRAIN': 'bh',
                'BANGLADESH': 'bd',
                'BARBADOS': 'bb',
                'BELARUS': 'by',
                'BELGIUM': 'be',
                'BELIZE': 'bz',
                'BENIN': 'bj',
                'BHUTAN': 'bt',
                'BOLIVIA': 'bo',
                'BOSNIA AND HERZEGOVINA': 'ba',
                'BOTSWANA': 'bw',
                'BRAZIL': 'br',
                'BRUNEI': 'bn',
                'BULGARIA': 'bg',
                'BURKINA FASO': 'bf',
                'BURUNDI': 'bi',
                'CABO VERDE': 'cv',
                'CAMBODIA': 'kh',
                'CAMEROON': 'cm',
                'CANADA': 'ca',
                'CENTRAL AFRICAN REPUBLIC': 'cf',
                'CHAD': 'td',
                'CHILE': 'cl',
                'CHINA': 'cn',
                'COLOMBIA': 'co',
                'COMOROS': 'km',
                'CONGO, DEMOCRATIC REPUBLIC OF THE': 'cd',
                'CONGO, REPUBLIC OF THE': 'cg',
                'COSTA RICA': 'cr',
                'CROATIA': 'hr',
                'CUBA': 'cu',
                'CYPRUS': 'cy',
                'CZECHIA': 'cz',
                'DENMARK': 'dk',
                'DJIBOUTI': 'dj',
                'DOMINICA': 'dm',
                'DOMINICAN REPUBLIC': 'do',
                'ECUADOR': 'ec',
                'EGYPT': 'eg',
                'EL SALVADOR': 'sv',
                'EQUATORIAL GUINEA': 'gq',
                'ERITREA': 'er',
                'ESTONIA': 'ee',
                'ESWATINI': 'sz',
                'ETHIOPIA': 'et',
                'FIJI': 'fj',
                'FINLAND': 'fi',
                'FRANCE': 'fr',
                'GABON': 'ga',
                'GAMBIA': 'gm',
                'GEORGIA': 'ge',
                'GERMANY': 'de',
                'GHANA': 'gh',
                'GREECE': 'gr',
                'GRENADA': 'gd',
                'GUATEMALA': 'gt',
                'GUINEA': 'gn',
                'GUINEA-BISSAU': 'gw',
                'GUYANA': 'gy',
                'HAITI': 'ht',
                'HONDURAS': 'hn',
                'HUNGARY': 'hu',
                'HONG KONG': 'hk',  // Changed to all uppercase
                'ICELAND': 'is',
                'INDIA': 'in',
                'INDONESIA': 'id',
                'IRAN': 'ir',
                'IRAQ': 'iq',
                'IRELAND': 'ie',
                'ISRAEL': 'il',
                'ITALY': 'it',
                'JAMAICA': 'jm',
                'JAPAN': 'jp',
                'JORDAN': 'jo',
                'KAZAKHSTAN': 'kz',
                'KENYA': 'ke',
                'KIRIBATI': 'ki',
                'KOREA, NORTH': 'kp',
                'SOUTH KOREA': 'kr',
                'KUWAIT': 'kw',
                'KYRGYZSTAN': 'kg',
                'LAOS': 'la',
                'LATVIA': 'lv',
                'LEBANON': 'lb',
                'LESOTHO': 'ls',
                'LIBERIA': 'lr',
                'LIBYA': 'ly',
                'LIECHTENSTEIN': 'li',
                'LITHUANIA': 'lt',
                'LUXEMBOURG': 'lu',
                'MADAGASCAR': 'mg',
                'MALAWI': 'mw',
                'MALAYSIA': 'my',
                'MALDIVES': 'mv',
                'MALI': 'ml',
                'MALTA': 'mt',
                'MARSHALL ISLANDS': 'mh',
                'MAURITANIA': 'mr',
                'MAURITIUS': 'mu',
                'MEXICO': 'mx',
                'MICRONESIA': 'fm',
                'MOLDOVA': 'md',
                'MONACO': 'mc',
                'MONGOLIA': 'mn',
                'MONTENEGRO': 'me',
                'MOROCCO': 'ma',
                'MOZAMBIQUE': 'mz',
                'MYANMAR': 'mm',
                'NAMIBIA': 'na',
                'NAURU': 'nr',
                'NEPAL': 'np',
                'NETHERLANDS': 'nl',
                'NEW ZEALAND': 'nz',
                'NICARAGUA': 'ni',
                'NIGER': 'ne',
                'NIGERIA': 'ng',
                'NORWAY': 'no',
                'OMAN': 'om',
                'PAKISTAN': 'pk',
                'PALAU': 'pw',
                'PALESTINE': 'ps',
                'PANAMA': 'pa',
                'PAPUA NEW GUINEA': 'pg',
                'PARAGUAY': 'py',
                'PERU': 'pe',
                'PHILIPPINES': 'ph',
                'POLAND': 'pl',
                'PORTUGAL': 'pt',
                'QATAR': 'qa',
                'ROMANIA': 'ro',
                'RUSSIA': 'ru',
                'RWANDA': 'rw',
                'SAINT KITTS AND NEVIS': 'kn',
                'SAINT LUCIA': 'lc',
                'SAINT VINCENT AND THE GRENADINES': 'vc',
                'SAMOA': 'ws',
                'SAN MARINO': 'sm',
                'SAO TOME AND PRINCIPE': 'st',
                'SAUDI ARABIA': 'sa',
                'SENEGAL': 'sn',
                'SERBIA': 'rs',
                'SEYCHELLES': 'sc',
                'SIERRA LEONE': 'sl',
                'SINGAPORE': 'sg',
                'SLOVAKIA': 'sk',
                'SLOVENIA': 'si',
                'SOLOMON ISLANDS': 'sb',
                'SOMALIA': 'so',
                'SOUTH AFRICA': 'za',
                'SOUTH SUDAN': 'ss',
                'SPAIN': 'es',
                'SRI LANKA': 'lk',
                'SUDAN': 'sd',
                'SURINAME': 'sr',
                'SWEDEN': 'se',
                'SWITZERLAND': 'ch',
                'SYRIA': 'sy',
                'TAIWAN': 'tw',
                'TAJIKISTAN': 'tj',
                'TANZANIA': 'tz',
                'THAILAND': 'th',
                'TIMOR-LESTE': 'tl',
                'TOGO': 'tg',
                'TONGA': 'to',
                'TRINIDAD AND TOBAGO': 'tt',
                'TUNISIA': 'tn',
                'TURKEY': 'tr',
                'TURKMENISTAN': 'tm',
                'TUVALU': 'tv',
                'UGANDA': 'ug',
                'UKRAINE': 'ua',
                'UNITED ARAB EMIRATES': 'ae',
                'UNITED KINGDOM': 'gb',
                'UNITED STATES': 'us',
                'URUGUAY': 'uy',
                'UZBEKISTAN': 'uz',
                'VANUATU': 'vu',
                'VATICAN CITY': 'va',
                'VENEZUELA': 've',
                'VIETNAM': 'vn',
                'YEMEN': 'ye',
                'ZAMBIA': 'zm',
                'ZIMBABWE': 'zw'
            };
            const normalized = countryName.trim().toUpperCase();
            return countryCodes[normalized] || 'unknown';
        }

        // ========= 2) LOADER HELPERS =========
        function showLoader() {
            $('#flightsContainer').html(`
                <div class="control-card text-center">
                    <div class="loading-spinner mx-auto mb-3"></div>
                    <div style="color: #6b7280; font-weight: 500;">Loading flight data...</div>
                </div>
            `);
        }
        function hideLoader() {
            $('.loader, .loading-spinner').parent().remove();
        }

        // ========= 3) FETCH ROUNDS WHEN FLIGHT LIST CHANGES =========
        $('#flightListName').on('change', function () {
            const selectedFlightListName = $(this).val();
            if (selectedFlightListName) {
                $('#roundFilter')
                    .prop('disabled', true)
                    .html('<option value="" disabled selected>Select Round</option>');

                $.ajax({
                    url: 'fetch_rounds.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        'event_ids[]': eventIds,
                        flight_list_name: selectedFlightListName
                    },
                    traditional: true,
                    success: function (response) {
                        let rounds;
                        try {
                            rounds = JSON.parse(response);
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            alert('Failed to fetch rounds.');
                            return;
                        }

                        if (rounds.length > 0) {
                            let options = '<option value="" disabled selected>Select Round</option>';
                            rounds.forEach(function (round) {
                                options += `<option value="${round.round_number}">Round ${round.round_number}</option>`;
                            });
                            $('#roundFilter').html(options).prop('disabled', false);
                        } else {
                            alert('No rounds found for the selected flight list name.');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Failed to fetch rounds:', error);
                        alert('Failed to fetch rounds. Please try again.');
                    }
                });
            }
        });

        // ========= 4) FETCH FLIGHTS WHEN ROUND CHANGES =========
        $('#roundFilter').on('change', function () {
            const selectedRound = $(this).val();
            const selectedFlightListName = $('#flightListName').val();

            if (selectedRound && selectedFlightListName) {
                showLoader();
                $.ajax({
                    url: 'fetch_flights.php',
                    type: 'GET',
                    data: {
                        event_mgm_id: eventMgmId,
                        event_ids: eventIds,
                        round_number: selectedRound,
                        flight_list_name: selectedFlightListName
                    },
                    success: function (response) {
                        hideLoader();
                        let flightData;
                        try {
                            flightData = JSON.parse(response);
                        } catch (e) {
                            console.error('Invalid JSON response:', e);
                            $('#flightsContainer').html(
                                '<div class="alert alert-danger text-center">Invalid response from server.</div>'
                            );
                            return;
                        }

                        if (flightData.error) {
                            alert(flightData.error);
                        } else if (!flightData.flights || flightData.flights.length === 0) {
                            $('#flightsContainer').html(
                                '<div class="no-records-wrapper"><div class="no-records-message">No flights found.</div></div>'
                            );
                        } else {
                            renderFlights(
                                flightData.flights,
                                selectedRound,
                                selectedFlightListName,
                                flightData.display_score
                            );
                        }
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        console.error('Failed to fetch flights:', error);
                        $('#flightsContainer').html(
                            '<div class="alert alert-danger text-center">Failed to fetch flight data. Please try again.</div>'
                        );
                    }
                });
            }
        });

        // ========= 5) RENDER FLIGHTS (with Flag column, no "No" column) =========
        function renderFlights(flightData, roundNumber, flightListName, displayScore) {
            console.log('Flight Data:', flightData);
            console.log('Display Score:', displayScore);

            const flightsContainer = $('#flightsContainer');
            flightsContainer.empty();

            if (flightData.length === 0) {
                flightsContainer.append(
                    '<div class="no-records-wrapper"><div class="no-records-message">No records found.</div></div>'
                );
                return;
            }

            // Scan the entire data for a real marker
            const hasMarkerAssignments = flightData.some(flight => 
                flight.marker_name && flight.marker_name !== 'N/A' && flight.marker_name.trim() !== ''
            );

            // Get the flight date from the first flight
            const flightDate = flightData[0].flight_date;
            const formattedDate = formatDate(flightDate);

            // Group flights by start_point
            const groupedFlightsByStartPoint = {};
            flightData.forEach(flight => {
                if (!groupedFlightsByStartPoint[flight.start_point]) {
                    groupedFlightsByStartPoint[flight.start_point] = [];
                }
                groupedFlightsByStartPoint[flight.start_point].push(flight);
            });

            // For each start point, render
            for (const startPoint in groupedFlightsByStartPoint) {
                const flights = groupedFlightsByStartPoint[startPoint];
                const groupedFlights = {};

                // Collect the course names
                const courseNames = new Set();
                flights.forEach(flight => {
                    const cId = String(flight.course_id);
                    const cName = courseIdNameMap[cId] || 'Unknown Course';
                    courseNames.add(cName);
                });
                const displayedCourseNames = Array.from(courseNames).join(', ');

                // Headers with new professional styling
                flightsContainer.append(`
                    <div class="section-header">
                        <h3 class="start-header">Round ${roundNumber} - ${ucfirst(startPoint)} Start</h3>
                        <div class="flight-date">Date: ${formattedDate}</div>
                        <div class="course-name">Course: ${displayedCourseNames}</div>
                    </div>
                `);

                // Group by flight_name
                flights.forEach(flight => {
                    if (!groupedFlights[flight.flight_name]) {
                        groupedFlights[flight.flight_name] = [];
                    }
                    groupedFlights[flight.flight_name].push(flight);
                });

                // Decide which rendering function to use
                const normalizedStartPoint = startPoint.trim().toLowerCase();
                if (normalizedStartPoint.includes('shotgun')) {
                    renderShotgunGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments);
                } else if (normalizedStartPoint.includes('one point')) {
                    renderOnePointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments);
                } else if (normalizedStartPoint.includes('two point')) {
                    renderTwoPointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments);
                } else if (normalizedStartPoint.includes('four point')) {
                    renderFourPointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments);
                } else if (normalizedStartPoint.includes('customize')) {
                    renderCustomizeGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments);
                } else {
                    console.error('Unknown start point:', startPoint);
                }
            }
        }

        // ========= 5A) DATE FORMATTER =========
        function formatDate(dateString) {
            const dateObj = new Date(dateString);
            if (isNaN(dateObj.getTime())) {
                return 'Invalid Date';
            }
            const day = String(dateObj.getDate()).padStart(2, '0');
            const year = dateObj.getFullYear();
            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            const monthName = monthNames[dateObj.getMonth()];
            return `${day} ${monthName} ${year}`;
        }

        // ========= 5B) TIME FORMATTER =========
        function formatTimeToAMPM(time) {
            const date = new Date('1970-01-01T' + time);
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            return hours + ':' + minutes + ' ' + ampm;
        }

        // ========= 5C) HELPER: CAP FIRST LETTER =========
        function ucfirst(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // ========= 6) SHOTGUN RENDERING (FLAG column) =========
        function getTeeBoxNumber(str) {
            str = (str || '').trim();
            if (/^\d+$/.test(str)) {
                return parseInt(str, 10);
            }
            const match = str.match(/\d+/);
            return match ? parseInt(match[0], 10) : 0;
        }

        function renderShotgunGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightId in groupedFlights) {
                const flightDetails = groupedFlights[flightId];
                const fInfo = flightDetails[0]; 
                const teeBox = fInfo.tee_box || 'N/A';
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }
                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: fInfo.flight_name,
                    flightTime: fInfo.flight_time
                });
            }

            // Sort TeeBoxes by numeric portion
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                return getTeeBoxNumber(a) - getTeeBoxNumber(b);
            });

            // Render each teeBox
            sortedTeeBoxes.forEach(teeBox => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append(`<h5>${teeBox}</h5>`);

                teeFlights.forEach(flight => {
                    flightDetailDiv.append(
                        `<h6><strong>${flight.flightName} (Time: ${formatTimeToAMPM(flight.flightTime)})</strong></h6>`
                    );

                    const table = $(`
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th> <!-- Flag column -->
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'HCP'}</th>
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');
                    flight.flightDetails.forEach(player => {
                        tbody.append(`
                            <tr>
                                <td>
                                    ${
                                      player.country 
                                        ? `<span class="flag-icon flag-icon-${getCountryCode(player.country.trim())}"></span>`
                                        : ''
                                    }
                                </td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                <td>${displayScore === 1 
                                    ? (player.total_strokes || '') 
                                    : (player.player_handicap || 'N/A')
                                }</td>
                                <td>${player.category || 'N/A'}</td>
                                ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                            </tr>
                        `);
                    });
                    flightDetailDiv.append(table);
                });

                // Decide which column
                const teeNum = getTeeBoxNumber(teeBox);
                if (teeNum <= 9) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1);
            row.append(col2);
            $('#flightsContainer').append(row);
        }

        // ========= 7) ONE POINT RENDERING (FLAG column) =========
        function renderOnePointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments) {
            const col1 = $('<div class="col-lg-12 mb-3"></div>'); 
            
            // Sort flightIds
            const sortedFlightIds = Object.keys(groupedFlights).sort((a, b) => {
                const numA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const numB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return numA - numB;
            });

            sortedFlightIds.forEach(flightId => {
                const flightDetails = groupedFlights[flightId];
                const fInfo = flightDetails[0] || {};

                const flightDetailDiv = $(`
                    <div class="flight-detail">
                        <h5>${fInfo.tee_box || 'N/A'}</h5>
                        <h6><strong>${fInfo.flight_name} (Time: ${formatTimeToAMPM(fInfo.flight_time)})</strong></h6>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th> <!-- Flag column -->
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'HCP'}</th>
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                `);

                const tbody = flightDetailDiv.find('tbody');
                flightDetails.forEach(player => {
                    tbody.append(`
                        <tr>
                            <td>
                                ${
                                  player.country
                                    ? `<span class="flag-icon flag-icon-${getCountryCode(player.country.trim())}"></span>`
                                    : ''
                                }
                            </td>
                            <td>${player.player_name || 'No player assigned'}</td>
                            <td>${
                                displayScore === 1 
                                    ? (player.total_strokes || '') 
                                    : (player.player_handicap || 'N/A')
                            }</td>
                            <td>${player.category || 'N/A'}</td>
                            ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                        </tr>
                    `);
                });

                col1.append(flightDetailDiv);
            });

            const row = $('<div class="row"></div>');
            row.append(col1);
            $('#flightsContainer').append(row);
        }

        // ========= 8) TWO POINT RENDERING (FLAG column) =========
        function renderTwoPointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');

            // Sort flight IDs
            const sortedFlightIds = Object.keys(groupedFlights).sort((a, b) => {
                const numA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const numB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return numA - numB;
            });

            // Group by TeeBox
            const groupedByTeeBox = {};
            sortedFlightIds.forEach(flightId => {
                const flightDetails = groupedFlights[flightId];
                const fInfo = flightDetails[0];
                const teeBox = fInfo.tee_box || 'N/A';
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }
                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: fInfo.flight_name,
                    flightTime: fInfo.flight_time
                });
            });

            // Sort TeeBoxes (assumes "TeeBox X" format)
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const teeA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const teeB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return teeA - teeB;
            });

            // Render
            sortedTeeBoxes.forEach((teeBox, index) => {
                const teeFlights = groupedByTeeBox[teeBox];
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append(`<h5>${teeBox}</h5>`);

                teeFlights.forEach(flight => {
                    flightDetailDiv.append(
                        `<h6><strong>${flight.flightName} (Time: ${formatTimeToAMPM(flight.flightTime)})</strong></h6>`
                    );

                    const table = $(`
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th> <!-- Flag column -->
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'HCP'}</th>
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    `);

                    let tbody = table.find('tbody');
                    flight.flightDetails.forEach(player => {
                        tbody.append(`
                            <tr>
                                <td>
                                    ${
                                      player.country
                                        ? `<span class="flag-icon flag-icon-${getCountryCode(player.country.trim())}"></span>`
                                        : ''
                                    }
                                </td>
                                <td>${player.player_name || 'No player assigned'}</td>
                                <td>${
                                    displayScore === 1 
                                      ? (player.total_strokes || '') 
                                      : (player.player_handicap || 'N/A')
                                }</td>
                                <td>${player.category || 'N/A'}</td>
                                ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                            </tr>
                        `);
                    });
                    flightDetailDiv.append(table);
                });

                if (index % 2 === 0) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1, col2);
            $('#flightsContainer').append(row);
        }

        // ========= 9) FOUR POINT RENDERING (FLAG column) =========
        function renderFourPointGroupBoxes(groupedFlights, displayScore, hasMarkerAssignments) {
            const col1 = $('<div class="col-lg-6 mb-3"></div>');
            const col2 = $('<div class="col-lg-6 mb-3"></div>');

            // Group flights by TeeBox
            const groupedByTeeBox = {};
            for (const flightName in groupedFlights) {
                const flightDetails = groupedFlights[flightName];
                const flightInfo = flightDetails[0];
                const teeBox = flightInfo.tee_box;
                if (!groupedByTeeBox[teeBox]) {
                    groupedByTeeBox[teeBox] = [];
                }
                groupedByTeeBox[teeBox].push({
                    flightDetails: flightDetails,
                    flightName: flightInfo.flight_name,
                    flightTime: flightInfo.flight_time
                });
            }

            // Sort TeeBoxes by numeric portion
            const sortedTeeBoxes = Object.keys(groupedByTeeBox).sort((a, b) => {
                const teeA = parseInt(a.replace(/[^\d]/g, ''), 10);
                const teeB = parseInt(b.replace(/[^\d]/g, ''), 10);
                return teeA - teeB;
            });

            // Render each teeBox
            sortedTeeBoxes.forEach((teeBox, index) => {
                // Sort teeFlights by time (earliest first)
                let teeFlights = groupedByTeeBox[teeBox].slice();
                teeFlights.sort((a, b) => {
                    // a.flightTime and b.flightTime are in 'HH:mm' or 'HH:mm:ss' format
                    const timeA = a.flightTime.length === 5 ? a.flightTime + ':00' : a.flightTime;
                    const timeB = b.flightTime.length === 5 ? b.flightTime + ':00' : b.flightTime;
                    return timeA.localeCompare(timeB);
                });
                const flightDetailDiv = $('<div class="flight-detail"></div>');
                flightDetailDiv.append(`<h5>${teeBox}</h5>`);

                teeFlights.forEach(flight => {
                    flightDetailDiv.append(
                        `<h6><strong>${flight.flightName} (Time: ${formatTimeToAMPM(flight.flightTime)})</strong></h6>`
                    );

                    const table = $(
                        `<div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th> <!-- Flag column -->
                                        <th>Player Name</th>
                                        <th>${displayScore === 1 ? 'Score' : 'HCP'}</th>
                                        <th>Cat</th>
                                        ${hasMarkerAssignments ? '<th class="marker-col">Mkr</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                    </div>
                `);

                    let tbody = table.find('tbody');
                    flight.flightDetails.forEach(player => {
                    tbody.append(`
                        <tr>
                            <td>
                                ${
                                  player.country
                                    ? `<span class="flag-icon flag-icon-${getCountryCode(player.country.trim())}"></span>`
                                    : ''
                                }
                            </td>
                            <td>${player.player_name || 'No player assigned'}</td>
                            <td>${
                                displayScore === 1 
                                    ? (player.total_strokes || '') 
                                    : (player.player_handicap || 'N/A')
                            }</td>
                            <td>${player.category || 'N/A'}</td>
                            ${hasMarkerAssignments ? `<td class="marker-col">${player.marker_name || 'N/A'}</td>` : ''}
                        </tr>
                    `);
                    });
                    flightDetailDiv.append(table);
                });

                // Assign to columns based on tee box number
                const teeNum = parseInt(teeBox.replace(/[^\d]/g, ''), 10);
                if (teeNum <= 9) {
                    col1.append(flightDetailDiv);
                } else {
                    col2.append(flightDetailDiv);
                }
            });

            const row = $('<div class="row"></div>');
            row.append(col1, col2);
            $('#flightsContainer').append(row);
        }

        // ========= 10) PRINT BUTTON =========
        $('#printButton').click(function () {
            const selectedRound = $('#roundFilter').val();
            const selectedFlightListName = $('#flightListName').val();

            if (!selectedRound || !selectedFlightListName) {
                alert('Please select both round and flight list name before printing.');
                return;
            }
            const printUrl = `print_flight.php?event_mgm_id=${eventMgmId}&round_number=${selectedRound}&flight_list_name=${encodeURIComponent(selectedFlightListName)}`;
            window.location.href = printUrl;
        });

        // ========= 11) PRINT SCORECARD BUTTON =========
        $('#printScorecardButton').click(function () {
            const selectedRound = $('#roundFilter').val();
            const selectedFlightListName = $('#flightListName').val();

            if (!selectedRound || !selectedFlightListName) {
                alert('Please select both round and flight list name before printing the scorecard.');
                return;
            }
            const printUrl = `print_scorecard.php?event_mgm_id=${eventMgmId}&round_number=${selectedRound}&flight_list_name=${encodeURIComponent(selectedFlightListName)}`;
            window.location.href = printUrl;
        });

        // ========= 12) EXPORT BUTTON =========
        $('#exportButton').click(function () {
            const selectedRound = $('#roundFilter').val();
            const selectedFlightListName = $('#flightListName').val();

            if (!selectedRound || !selectedFlightListName) {
                alert('Please select both round and flight list name before exporting.');
                return;
            }

            // Create form and submit it
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = 'export_flight_timetable.php';

            // Add parameters
            const params = {
                event_mgm_id: eventMgmId,
                round_number: selectedRound,
                flight_list_name: selectedFlightListName
            };

            for (const key in params) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = params[key];
                form.appendChild(input);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        });

    });
    </script>

</body>
</html>
