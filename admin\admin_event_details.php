<?php
require("../database.php");


$event_id = isset($_GET['event_id']) ? $_GET['event_id'] : '';


$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as count FROM event_role_mgm WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($result_found);
    $stmt->fetch();
    $stmt->close();

    if ($result_found > 0) {
        // Get all event_mgm_id for this profile_id
        $eventMgmIds = [];
        $query = "SELECT event_mgm_id FROM event_role_mgm WHERE profile_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $session_profile_id);
        $stmt->execute();
        $stmt->bind_result($event_mgm_id);
        while ($stmt->fetch()) {
            $eventMgmIds[] = $event_mgm_id;
        }
        $stmt->close();

        // Fetch all event_id linked to these event_mgm_id
        $eventIds = [];
        if (!empty($eventMgmIds)) {
            $placeholders = implode(',', array_fill(0, count($eventMgmIds), '?'));
            $query = "SELECT event_id FROM event_mgm WHERE event_mgm_id IN ($placeholders)";
            $stmt = $conn->prepare($query);

            // Dynamically bind parameters
            $types = str_repeat('s', count($eventMgmIds));
            $stmt->bind_param($types, ...$eventMgmIds);
            $stmt->execute();
            $stmt->bind_result($fetched_event_id);
            while ($stmt->fetch()) {
                $eventIds[] = $fetched_event_id;
            }
            $stmt->close();
        }

        // Check if $event_id matches the allowed event IDs
        if (!in_array($event_id, $eventIds)) {
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    } elseif ($admin_roleid != 'gPHOfKV0sL') {
        // Redirect if admin_roleid is not gPHOfKV0sL and session_profile_id not found in event_role_mgm
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
$status = [
    'event_publish' => 'green', // Always green since the page is accessible only after event creation
    'course_configuration' => 'red', // Default status
    'pending_payment' => 'red', // Default status for pending payment
    'attendance' => 'red', // Default status for attendance
    'flight_list' => 'red', // Default status for flight list
];

if ($event_id) {
    // Step 1: Fetch the event_mgm_id for the given event_id
    $event_mgm_query = "SELECT event_mgm_id FROM event_mgm WHERE event_id = ?";
    $stmt1 = $conn->prepare($event_mgm_query);
    $stmt1->bind_param("s", $event_id);
    $stmt1->execute();
    $result = $stmt1->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $event_mgm_id = $row['event_mgm_id'];

        $event_publish_query = "
        SELECT
            MIN(event_publish) AS min_publish,
            MAX(event_publish) AS max_publish
        FROM event_mgm
        WHERE event_mgm_id = ?
        ";
        
        // Prepare the statement
        $stmt = $conn->prepare($event_publish_query);
        
        // Bind the parameter
        $stmt->bind_param("s", $event_mgm_id);
        
        // Execute the statement
        $stmt->execute();
        
        // Get the result
        $event_publish_result = $stmt->get_result();
        
        // Fetch the data
        if ($event_publish_result) {
            $event_publish_row = $event_publish_result->fetch_assoc();
        
            $min_publish = $event_publish_row['min_publish'];
            $max_publish = $event_publish_row['max_publish'];
        
            $status['event_publish'] = ($min_publish == 1 && $max_publish == 1) ? 'green' : 
                                       (($max_publish == 1) ? 'yellow' : 'red');
        
        } else {
            // Handle query error
            echo "Error executing query: " . $conn->error;
        }
        
        // Close the statement
        $stmt->close();
        // Step 2: Fetch all event_id and category_id for the given event_mgm_id
        $event_query = "SELECT event_id, category_id FROM event_mgm WHERE event_mgm_id = ?";
        $stmt2 = $conn->prepare($event_query);
        $stmt2->bind_param("s", $event_mgm_id);
        $stmt2->execute();
        $event_result = $stmt2->get_result();

        $all_course_found = true;
        $any_course_found = false;

        // Step 3: Check course configuration status in event_category_tee_box
        while ($event_row = $event_result->fetch_assoc()) {
            $event_id = $event_row['event_id'];
            $category_id = $event_row['category_id'];

            $check_query = "SELECT COUNT(*) as count FROM event_category_tee_box WHERE event_id = ? AND category_id = ?";
            $stmt3 = $conn->prepare($check_query);
            $stmt3->bind_param("ss", $event_id, $category_id);
            $stmt3->execute();
            $check_result = $stmt3->get_result();
            $check_count = $check_result->fetch_assoc()['count'];

            if ($check_count > 0) {
                $any_course_found = true;
            } else {
                $all_course_found = false;
            }
        }

        // Set the course configuration status based on findings
        $status['course_configuration'] = $all_course_found ? 'green' : ($any_course_found ? 'yellow' : 'red');

        // Step 4: Check pending payment status in payment_details
        $all_payments_completed = true;
        $any_payment_pending = false;

        $event_result->data_seek(0); // Reset pointer for loop reuse
        while ($event_row = $event_result->fetch_assoc()) {
            $event_id = $event_row['event_id'];

            $payment_query = "SELECT payment_status FROM payment_details WHERE event_id = ?";
            $stmt4 = $conn->prepare($payment_query);
            $stmt4->bind_param("s", $event_id);
            $stmt4->execute();
            $payment_result = $stmt4->get_result();

            if ($payment_result->num_rows > 0) {
                while ($payment_row = $payment_result->fetch_assoc()) {
                    if ($payment_row['payment_status'] == 0) {
                        $any_payment_pending = true;
                        $all_payments_completed = false;
                    }
                }
            } else {
                $all_payments_completed = false;
            }
        }

        // Set pending payment status
        $status['pending_payment'] = $all_payments_completed ? 'green' : ($any_payment_pending ? 'yellow' : 'red');

        // Step 6: Check flight list status
        $all_flights_assigned = true;
        $any_flight_missing = false;

        $event_result->data_seek(0);
        while ($event_row = $event_result->fetch_assoc()) {
            $event_id = $event_row['event_id'];
            $category_id = $event_row['category_id'];

            $form_query = "SELECT form_id FROM registration_form WHERE event_id = ? AND category_id = ?";
            $stmt5 = $conn->prepare($form_query);
            $stmt5->bind_param("ss", $event_id, $category_id);
            $stmt5->execute();
            $form_result = $stmt5->get_result();

            if ($form_result->num_rows > 0) {
                while ($form_row = $form_result->fetch_assoc()) {
                    $form_id = $form_row['form_id'];

                    $flight_query = "SELECT COUNT(*) as count FROM flight_list WHERE form_id = ?";
                    $stmt6 = $conn->prepare($flight_query);
                    $stmt6->bind_param("s", $form_id);
                    $stmt6->execute();
                    $flight_result = $stmt6->get_result();
                    $flight_count = $flight_result->fetch_assoc()['count'];

                    if ($flight_count == 0) {
                        $all_flights_assigned = false;
                        $any_flight_missing = true;
                    }
                }
            } else {
                $all_flights_assigned = false;
            }
        }

        // Set flight list status
        $status['flight_list'] = $all_flights_assigned ? 'green' : ($any_flight_missing ? 'yellow' : 'red');

    } else {
        echo "No matching event found!";
        exit;
    }
} else {
    echo "Invalid event ID!";
    exit();
}

// Pass status data to frontend
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" >
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css" />

    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .header-section {
            background-color: white;
            padding: 30px;
            width: 100%;
            text-align: center;
            border: none;
            border-radius: 10px;
        }

        .header-section:hover,
        .content-section:hover,
        .info-box:hover  {
            transform: translateY(-5px);
            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
        }

        /* Main title styles */
        .main-title {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 20px;
            color: #1f2937;
        }

        .subtitle {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #374151;
        }

        /* Enroll button styles */
        .enroll-button {
            background-color: #1f2937;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }

        .enroll-button:hover {
            background-color: #4b5563;
        }

        /* Content section styles */
        .content-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.05);
        }

        .content-section h2 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 20px;
        }
        /* Info box styles */
        .info-box {
            text-align: center;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .info-box .label {
            font-size: 16px;
            color: #1f2937;
            font-weight: 600;
        }

        .enroll-button {
            background-color: #333;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        .enroll-button:hover {
            background-color: #555;
        }
        .content-section {
            margin-top: 40px;
        }
        .content-section p {
            margin-bottom: 20px;
        }
        .content-section img {
            width: 100%;
            height: auto;
            border-radius: 10px;
            margin-top: 20px;
        }
        .center-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Image error handling */
        .carousel-item img {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-item img[src*="default"] {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f8f9fa"/><text x="50" y="50" text-anchor="middle" dy=".3em" fill="%236c757d" font-family="Arial, sans-serif" font-size="12">Image Not Found</text></svg>');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        .center-image img {
            max-width: 100%;
            height: auto;
            margin: auto;
        }
        .player-requirement-section {
            display: flex;
            align-items: center;
            flex-direction: column;
        }
        .player-requirement-text {
            flex: 0.7;
            padding-left: 20px;
            text-align: left;
        }
        .player-requirement-image {
            flex: 0.3;
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .player-requirement-image img {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .status-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        @media (min-width: 768px) {
            .main-title {
                font-size: 28px;
                margin-bottom: 15px;
            }

            .enroll-button {
                padding: 10px 20px;
                font-size: 14px;
            }

            .info-box .label {
                font-size: 14px;
            }

            footer {
                text-align: center;
            }
            
            .player-requirement-section {
                flex-direction: row;
            }
            .player-requirement-text {
                order: 2;
            }
            .player-requirement-image {
                order: 1;
                margin-bottom: 0;
            }
            .status-wrapper {
                flex-direction: row;
                align-items: center;
            }
        }

        /* Slide-in animations */
        .fade-in-left, .fade-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .fade-in-right {
            transform: translateX(-50px);
        }

        .fade-in-left.visible, .fade-in-right.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            min-width: 150px;
            border-radius: 5px;
            overflow: hidden;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .dropdown-menu a {
            padding: 10px 20px;
            display: block;
            color: #000;
            text-decoration: none;
            white-space: nowrap;
        }
        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        .dropdown {
            position: relative;
        }
        .dropdown .dropdown-menu {
            display: none;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        .dropdown-menu a {
            display: block;
            padding: 10px 20px;
            color: #000;
            text-decoration: none;
        }
        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
        .event-setup-progress-bar {
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.progress-step {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    min-width: 80px;
    padding: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-bottom: 5px;
}

.status-dot.green { background-color: green; }
.status-dot.yellow { background-color: orange; }
.status-dot.red { background-color: red; }

/* Progress line styling */
.progress-line {
    flex: 1;
    height: 2px;
    background-color: #e5e7eb;
    margin: 0 5px;
}

.progress-step p {
    margin: 0;
    font-size: 14px;
    color: #374151;
}

/* Tooltip Styling */
.tooltip-text {
    visibility: hidden;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    padding: 5px 8px;
    position: absolute;
    z-index: 1;
    bottom: 150%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.tooltip-text::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

/* Show Tooltip on Hover */
.progress-step:hover .tooltip-text,
.progress-step:focus .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Mobile-friendly adjustments */
@media (max-width: 768px) {
    .progress-steps {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .progress-step {
        flex-direction: row;
        align-items: center;
    }

    .progress-step p {
        font-size: 16px;
        margin-left: 10px;
    }

    .progress-line {
        display: none; /* Hide horizontal line on mobile */
    }

    .status-dot {
        width: 15px;
        height: 15px;
    }
}
    </style>
</head>

<body>
    <div id="loading-screen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
   <!-- Header -->
   <?php include("admin_header.php"); ?>
   
    <div class="container mt-5">
        <?php
        // $sql = "SELECT event_id, event_name, event_venue, event_start_date, event_end_date 
        // FROM event_mgm 
        // WHERE event_mgm_id = ?";
        $sql = "SELECT event_mgm.event_id, event_mgm.event_name, club_info.golf_club_name AS event_venue, event_mgm.event_start_date, event_mgm.event_end_date
        FROM event_mgm
        JOIN club_info ON event_mgm.event_venue = club_info.club_id
        WHERE event_mgm.event_mgm_id = ?";


        // Prepare the statement
        $stmt = $conn->prepare($sql);

        // Bind the event_mgm_id parameter
        $stmt->bind_param("s", $event_mgm_id);

        // Execute the query
        $stmt->execute();

        // Fetch the result
        $result = $stmt->get_result();

        // Check if any result is returned
        if ($result->num_rows > 0) {
            // Fetch the row
            $row = $result->fetch_assoc();

            ?>

        <div class="header-section text-center fade-in-left">
            <h1 class="main-title"><?php echo "" . $row['event_name'] . " ";?></h1>
            <?php
            $formatted_start_date = (new DateTime($row['event_start_date']))->format('d-m-Y');
            $formatted_end_date = (new DateTime($row['event_end_date']))->format('d-m-Y');
            ?>
            <p class="subtitle"><?php echo "" . $formatted_start_date  . " - " . $formatted_end_date  . "";?><br><?php echo "" . $row['event_venue'] . " ";?></p>
            <div class="button-group">
            <?php $event_id = isset($_GET['event_id']) ? $_GET['event_id'] : '';?>
            <a href="manage_event.php?event_id=<?php echo $event_id; ?>"><button class="enroll-button">Manage Event Details</button></a>

                <!--<a href="accommodation_reservation.html"><button class="enroll-button">Accommodation Reservation List</button></a>-->
            </div>
        </div>
        <?php
        } else {
            echo "No event found with the provided event_mgm_id.";
        }
        
        // Close the statement
        $stmt->close();
        ?>
        
         <!-- Info Boxes -->
         <div class="row mt-5 text-center fade-in-right">
            <div class="col-md-2">
            <a href="participants_list.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Participants List</div>
                    </div>
                </a>
            </div>
           <!-- <div class="col-md-2">
                <a href="waiting_list.html">
                    <div class="info-box">
                        <div class="label">Waiting List</div>
                    </div>
                </a>
            </div>-->
            <div class="col-md-2">
                <a href="attendance_list.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Attendance</div>
                    </div>
                </a>
            </div>
            <!--<div class="col-md-2">
                <a href="event_budget.html">
                    <div class="info-box">
                        <div class="label">Event Budget</div>
                    </div>
                </a>
            </div>-->
            <div class="col-md-2">
                <a href="result.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Result List</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="admin_scorecard.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Scorecard Center</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="flight_list.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Draw List</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="scorecardConfig_list.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Scorers Configuration</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="payment_configuration_list.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Payment Configuration</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="payment_verification.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Payment Verification</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="attachment_center.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Attachment Center</div>
                    </div>
                </a>
            </div>
            <div class="col-md-2">
                <a href="golf_shirt_config.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                    <div class="info-box">
                        <div class="label">Golf Shirt Config</div>
                    </div>
                </a>
            </div>
            
        </div>
        <?php
        $sql = "SELECT * 
        FROM event_information
        WHERE event_mgm_id = ?";

        // Prepare the statement
        $stmt = $conn->prepare($sql);

        // Bind the event_mgm_id parameter
        $stmt->bind_param("s", $event_mgm_id);

        // Execute the query
        $stmt->execute();

        // Fetch the result
        $result = $stmt->get_result();

        // Check if any result is returned
        if ($result->num_rows > 0) {
            // Fetch the row
            $row = $result->fetch_assoc();

            ?>
           <div class="container mt-5">
    <div class="event-setup-progress-bar">
        <h3 class="main-title text-center mb-4">Event Setup Progress</h3>
        <div class="progress-steps d-flex justify-content-between align-items-center">
            <!-- Each stage with status dot and label -->
            <div class="progress-step">
                <span class="status-dot <?php echo $status['event_publish']; ?>"></span>
                <p>Event Publish</p>
                <div class="tooltip-text">
                <?php echo ($status['event_publish'] == 'green') ? 'Published' : 'Not Publish'; ?>
            </div>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <span class="status-dot <?php echo $status['course_configuration']; ?>"></span>
                <p>Course Configuration</p>
                <div class="tooltip-text">
                <?php
                    if ($status['course_configuration'] == 'green') {
                        echo 'Completed';
                    } elseif ($status['course_configuration'] == 'yellow') {
                        echo 'Partially Configured';
                    } else {
                        echo 'Not Configured';
                    }
                ?>
            </div>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <span class="status-dot <?php echo $status['pending_payment']; ?>"></span>
                <p>Pending Payment</p>
                <div class="tooltip-text">
                <?php
                    if ($status['pending_payment'] == 'green') {
                        echo 'All Paid';
                    } elseif ($status['pending_payment'] == 'yellow') {
                        echo 'Partially Paid';
                    } else {
                        echo 'No Payments';
                    }
                ?>
            </div>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <span class="status-dot <?php echo $status['flight_list']; ?>"></span>
                <p>Flight Generation</p>
                <div class="tooltip-text">
                <?php
                    if ($status['flight_list'] == 'green') {
                        echo 'All Listed';
                    } elseif ($status['flight_list'] == 'yellow') {
                        echo 'Partially Listed';
                    } else {
                        echo 'Not Listed';
                    }
                ?>
            </div>
            </div>
           
        </div>
    </div>
</div>

        <div class="content-section fade-in-left">
            <h2 class="section-title">About Event</h2>
            <div class="row">
                <div class="col-md-8">
                    <p><?php echo "" . $row['event_des'] . " ";?></p>
                </div>
                <div class="col-md-4 center-image">
                    <div id="aboutCarousel" class="carousel slide" data-bs-ride="carousel"> 
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <?php
                                $event_image1 = trim($row['event_image1']);
                                $image_path = "../img/" . $event_image1;
                                $fallback_svg = "data:image/svg+xml;base64," . base64_encode('<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f8f9fa"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="18" fill="#6c757d" text-anchor="middle" dy=".3em">Event Image Not Available</text></svg>');

                                // Check if image file exists
                                if (!empty($event_image1) && file_exists($image_path)) {
                                    $image_src = $image_path;
                                } else {
                                    $image_src = $fallback_svg;
                                }
                                ?>
                                <img src="<?php echo $image_src; ?>"
                                     class="d-block w-100"
                                     alt="Event Image 1"
                                     style="height: 300px; object-fit: cover;">
                            </div>
                            <!--<div class="carousel-item">
                                <img src="https://cdn.shortpixel.ai/spai/w_865+q_+ret_img+to_webp/https://www.happygolflessons.com/wp-content/uploads/golf-kids.jpg" class="d-block w-100" alt="Event Image 2">
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#aboutCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#aboutCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="content-section fade-in-right">
            <h2 class="section-title">Player Requirement</h2>
            <div class="player-requirement-section">
                <div class="player-requirement-image">
                    <div id="playerRequirementCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <?php
                                $event_image2 = trim($row['event_image2']);
                                $image_path2 = "../img/" . $event_image2;
                                $fallback_svg2 = "data:image/svg+xml;base64," . base64_encode('<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f8f9fa"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="#6c757d" text-anchor="middle" dy=".3em">Player Requirement Image Not Available</text></svg>');

                                // Check if image file exists
                                if (!empty($event_image2) && file_exists($image_path2)) {
                                    $image_src2 = $image_path2;
                                } else {
                                    $image_src2 = $fallback_svg2;
                                }
                                ?>
                                <img src="<?php echo $image_src2; ?>"
                                     class="d-block w-100"
                                     alt="Player Requirement Image 1"
                                     style="height: 300px; object-fit: cover;">
                            </div>
                         <!-- Additional carousel items can be added here -->
                     
                        <button class="carousel-control-prev" type="button" data-bs-target="#playerRequirementCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#playerRequirementCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>-->
                    </div>
                    </div>
                </div>
                <div class="player-requirement-text">
                    <p><?php echo "" . $row['event_player_requirement'] . " ";?></p>
                </div>
            </div>
        </div>

        <div class="content-section fade-in-left">
            <h2 class="section-title">Rules and Regulations</h2>
           <p><?php echo "" . $row['event_rules_regulations'] . " ";?></p>
        </div>
        <?php
        } else {
            echo "";
        }
        
        // Close the statement
        $stmt->close();
        ?>

    </div>
    <!-- Footer -->
    <?php include("admin_footer.php"); ?>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
         // Function to toggle dropdown visibility
         function toggleDropdown(event) {
            event.preventDefault(); // Prevent the default action
            event.stopPropagation(); // Prevent the event from bubbling up

            const dropdownMenu = event.currentTarget.nextElementSibling;
            const icon = event.currentTarget.querySelector('i.fas');

            // Close all other open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                    const otherIcon = menu.previousElementSibling.querySelector('i.fas');
                    if (otherIcon) {
                        otherIcon.classList.remove('fa-chevron-up');
                        otherIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Toggle the current dropdown
            const isShown = dropdownMenu.classList.contains('show');
            dropdownMenu.classList.toggle('show', !isShown);
            icon.classList.toggle('fa-chevron-down', isShown);
            icon.classList.toggle('fa-chevron-up', !isShown);
        }

        // Function to close dropdown when clicking outside
        function closeDropdown(event) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    menu.classList.remove('show');
                    const icon = menu.previousElementSibling.querySelector('i.fas');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                }
            });
        }

        // Event listener to close dropdown on clicking outside
        document.addEventListener('click', closeDropdown);

        // Event listeners for each dropdown toggle button
        document.querySelectorAll('.dropdown > a').forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });


         //animation to display the section
         document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
            const sections = document.querySelectorAll('.fade-in-left, .fade-in-right');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });
        });

        //loading animation to makesure the content is well prepare
        window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('loading-screen');
        const sections = document.querySelectorAll('.fade-in-left, .fade-in-right');
        const options = {
            threshold: 0.1
        };

        const observer = new IntersectionObserver(function(entries, observer) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, options);

        sections.forEach(section => {
            observer.observe(section);
        });

        // Hide loading screen when content is fully loaded
        loadingScreen.style.display = 'none';
    });

    </script>
</body>

</html>
